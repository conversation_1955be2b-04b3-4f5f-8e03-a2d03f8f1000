import { DynamicModule, Module } from '@nestjs/common';
import { ClsModule } from 'nestjs-cls';

import { PrismaModule } from '../prisma/prisma.module';
import { ContextPopulationService, ContextService } from './context.service';

@Module({
  imports: [ClsModule, PrismaModule],
  providers: [ContextPopulationService, ContextService],
  exports: [ContextPopulationService, ContextService],
})
export class ContextModule {
  static forCli(): DynamicModule {
    return {
      module: ContextModule,
      imports: [
        ClsModule.forRoot({
          global: true,
          middleware: {
            mount: false,
          },
        }),
        PrismaModule,
      ],
      providers: [ContextPopulationService, ContextService],
      exports: [ContextPopulationService, ContextService],
    };
  }
}
