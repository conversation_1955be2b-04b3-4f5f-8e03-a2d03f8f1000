import type {
  IPharmacyService,
  MultiplePrescriptionRequest,
  MultiplePrescriptionResponse,
  PharmacyPatient,
  PharmacyPrescriber,
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import { CacheService } from '@modules/cache/cache.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { formatInTimeZone } from 'date-fns-tz';

import { PrescriptionImageGeneratorService } from './prescription-image-generator.service';

interface RedRockConfig {
  baseUrl: string;
  storeId: string;
  facilityId: string;
  chargeAccountId: string;
  username: string;
  password: string;
}

interface RedRockEntityPayload {
  StoreID: string;
  Entity: string;
  Field: string[];
}

interface RedRockEntityResponse {
  [key: string]: string;
}

@Injectable()
export class RedRockPharmacyService implements IPharmacyService {
  private readonly logger = new Logger(RedRockPharmacyService.name);
  private readonly TOKEN_CACHE_KEY_PREFIX = 'redrock:auth:token';

  constructor(
    private readonly configService: ConfigService,
    private readonly prescriptionImageGenerator: PrescriptionImageGeneratorService,
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
  ) {}

  async submitPrescriptions(
    request: MultiplePrescriptionRequest,
  ): Promise<MultiplePrescriptionResponse> {
    const results: MultiplePrescriptionResponse['results'] = [];

    // RedRock requires individual requests for each prescription
    for (const prescription of request.prescriptions) {
      try {
        // Get the appropriate store configuration based on patient state
        const config = this.getRedRockConfig(prescription);
        const storeId = config.storeId;

        // Filter products to only include those matching the correct store ID
        const filteredProducts = prescription.products.filter((product) => {
          if (!product.metadata || !product.metadata.storeId) return false;

          // Check if the product's storeId matches the config storeId
          const productStoreId = product.metadata.storeId.toString();
          const configStoreId = storeId.toString();
          return productStoreId === configStoreId;
        });

        if (filteredProducts.length === 0) {
          throw new Error(
            `No products found matching store ID ${storeId} for treatment ${prescription.treatmentId}`,
          );
        }

        // Create a new prescription request with filtered products
        const filteredPrescription = {
          ...prescription,
          products: filteredProducts,
        };

        const response = await this.processPrescription(filteredPrescription);

        results.push({
          treatmentId: prescription.treatmentId,
          prescriptionId: prescription.prescriptionId,
          success: response.success,
          orderId: response.orderId,
          pharmacyOrderId: response.pharmacyOrderId,
          message: response.message,
          errors: response.errors,
        });
      } catch (error) {
        this.logger.error(
          `Failed to submit prescription for treatment ${prescription.treatmentId}:`,
          error,
        );

        results.push({
          treatmentId: prescription.treatmentId,
          prescriptionId: prescription.prescriptionId,
          success: false,
          message: error.message || 'Unknown error',
          errors: [{ message: error.message || 'Unknown error' }],
        });
      }
    }

    const allSuccess = results.every((r) => r.success);
    const successCount = results.filter((r) => r.success).length;

    return {
      success: allSuccess,
      message: allSuccess
        ? `All ${results.length} prescriptions submitted successfully to RedRock`
        : `${successCount} of ${results.length} prescriptions submitted successfully to RedRock`,
      results,
      rawResponse: results.map((r) => r.pharmacyOrderId).filter(Boolean),
    };
  }

  private async processPrescription(request: PrescriptionRequest): Promise<{
    success: boolean;
    message?: string;
    orderId?: string;
    pharmacyOrderId?: string;
    errors?: Array<{ field?: string; message: string }>;
    rawResponse?: any;
  }> {
    const prescriptionResponses: any[] = [];
    const prescriptionOrderIds: string[] = [];

    try {
      const config = this.getRedRockConfig(request);
      const httpClient = this.createHttpClient(config.baseUrl, config.storeId);

      const token = await this.getAuthToken(
        httpClient,
        config.username,
        config.password,
        config.storeId,
      );
      const prescriberId = await this.getOrCreatePrescriber(
        httpClient,
        token,
        config.storeId,
        request.prescriber,
        request.patient.address.state,
      );
      const patientId = await this.getOrCreatePatient(
        httpClient,
        token,
        config.storeId,
        request.patient,
        config.facilityId,
        config.chargeAccountId,
      );

      for (const product of request.products) {
        let prescriptionPayload: any = null;
        let responseData: any = null;
        let responseStatus = 500;
        let individualOrderId: string | null = null;

        try {
          // Populate prescriber data with the same values we send to RedRock API
          const requestWithPrescriberData = {
            ...request,
            prescriber: {
              ...request.prescriber,
              address: {
                street1: 'Wilmington',
                city: 'Wilmington',
                state: 'DE',
                zipCode: '19801',
              },
              phoneNumber: '(*************',
            },
          };

          // Generate prescription image for this specific product
          const prescriptionImageBase64 =
            await this.prescriptionImageGenerator.generatePrescriptionImage(
              requestWithPrescriberData,
              {
                ...product,
                quantity: product.metadata.quantity,
              },
            );

          prescriptionPayload = this.buildPrescriptionPayload(
            config,
            patientId,
            prescriberId,
            {
              ...product,
              quantity: parseFloat(product.metadata.quantity as string),
            },
            request,
            prescriptionImageBase64,
          );

          // The following code will not execute due to the throw above
          const response = await httpClient.post(
            '/V3/IPS',
            prescriptionPayload,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            },
          );

          responseData = response.data;
          responseStatus = response.status || 200;
          individualOrderId = response.data?.PRESCRIPTIONRXID || null;

          prescriptionResponses.push(responseData);
          if (individualOrderId) {
            prescriptionOrderIds.push(individualOrderId);
          }

          // Log successful individual prescription submission
          await this.logPharmacyIntegration(
            request,
            prescriptionPayload,
            responseData,
            responseStatus,
            individualOrderId,
          );
        } catch (error) {
          // Handle individual prescription error
          if (error.isAxiosError && error.response) {
            responseStatus = error.response.status;
            responseData = error.response.data;
          }

          // Check if this is our temporary debug error
          if (error.message.includes('TEMPORARY: Forcing manual submission')) {
            this.logger.warn(
              `Intentionally failing RedRock submission for debugging: ${error.message}`,
            );
            // The prescription was already logged before throwing, so we don't need to log again
          } else {
            // Log failed individual prescription submission for real errors
            await this.logPharmacyIntegration(
              request,
              prescriptionPayload,
              responseData || error.message,
              responseStatus,
              null,
            );
          }

          // Continue processing other products even if one fails
          this.logger.error(
            `Failed to submit prescription for product ${product.id} to RedRock`,
            error,
          );
        }
      }

      // If all prescriptions failed, throw error
      if (prescriptionOrderIds.length === 0) {
        throw new Error('All prescriptions failed to submit to RedRock');
      }

      const pharmacyOrderIds = prescriptionOrderIds.join(',');

      return {
        success: true,
        orderId: request.treatmentId,
        pharmacyOrderId: pharmacyOrderIds,
        message: 'Prescription submitted successfully to RedRock',
        rawResponse: prescriptionResponses,
      };
    } catch (error) {
      this.logger.error('Failed to submit prescription to RedRock', error);

      if (error.isAxiosError && error.response) {
        const errorMessage = error.response.data?.message || error.message;

        return {
          success: false,
          orderId: request.treatmentId,
          message: `RedRock API error (${error.response.status}): ${errorMessage}`,
          errors: [
            {
              message: `RedRock API error (${error.response.status}): ${errorMessage}`,
            },
          ],
          rawResponse: error.response.data,
        };
      }

      return {
        success: false,
        orderId: request.treatmentId,
        message: `Failed to submit prescription: ${error.message}`,
        errors: [
          { message: `Failed to submit prescription: ${error.message}` },
        ],
      };
    }
  }

  private getRedRockConfig(request: PrescriptionRequest): RedRockConfig {
    const patientState = request.patient.address.state.toUpperCase();
    let isStGeorge = patientState === 'NV' || patientState === 'CA';
    const isProd =
      this.configService.get<string>('ENVIRONMENT') === 'production';
    if (!isProd) isStGeorge = false;

    if (isStGeorge) {
      return {
        baseUrl: this.configService.get<string>('REDROCK_STGEORGE_API_URL'),
        storeId: this.configService.get<string>('REDROCK_STGEORGE_STORE_ID'),
        facilityId: this.configService.get<string>(
          'REDROCK_STGEORGE_FACILITY_ID',
        ),
        chargeAccountId: this.configService.get<string>(
          'REDROCK_STGEORGE_CHARGE_ACCOUNT_ID',
        ),
        username: this.configService.get<string>('REDROCK_STGEORGE_USERNAME'),
        password: this.configService.get<string>('REDROCK_STGEORGE_PASSWORD'),
      };
    } else {
      return {
        baseUrl: this.configService.get<string>('REDROCK_SPRINGVILLE_API_URL'),
        storeId: this.configService.get<string>('REDROCK_SPRINGVILLE_STORE_ID'),
        facilityId: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_FACILITY_ID',
        ),
        chargeAccountId: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_CHARGE_ACCOUNT_ID',
        ),
        username: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_USERNAME',
        ),
        password: this.configService.get<string>(
          'REDROCK_SPRINGVILLE_PASSWORD',
        ),
      };
    }
  }

  private isStGeorgeStore(patientState: string): boolean {
    const state = patientState.toUpperCase();
    return state === 'NV' || state === 'CA';
  }

  private getClinicianColumnName(patientState: string): string {
    return this.isStGeorgeStore(patientState)
      ? 'redrockStGeorgeClinicianId'
      : 'redrockSpringvilleClinicianId';
  }

  private getPatientColumnName(patientState: string): string {
    return this.isStGeorgeStore(patientState)
      ? 'redrockStGeorgePatientId'
      : 'redrockSpringvillePatientId';
  }

  private createHttpClient(baseUrl: string, storeId: string): AxiosInstance {
    const httpClient = axios.create({ baseURL: baseUrl });

    // Add response interceptor to handle 401 responses
    httpClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Invalidate the cached token for this store
          const cacheKey = `${this.TOKEN_CACHE_KEY_PREFIX}:${storeId}`;
          await this.cacheService.del(cacheKey);
          this.logger.warn(
            `Auth token invalidated for store ${storeId} due to 401 response`,
          );
        }
        // Re-throw the error for proper error handling
        return Promise.reject(error);
      },
    );

    return httpClient;
  }

  private async getAuthToken(
    httpClient: AxiosInstance,
    username: string,
    password: string,
    storeId: string,
  ): Promise<string> {
    const cacheKey = `${this.TOKEN_CACHE_KEY_PREFIX}:${storeId}`;

    // Use flexible caching with 50 minutes fresh, 24 hours stale
    return this.cacheService.flexible<string>(
      cacheKey,
      [3600, 86400],
      async () => {
        try {
          const response = await httpClient.post<string>('/Auth/Authenticate', {
            username,
            password,
          });

          const token = response.data;
          if (!token) {
            throw new Error('No token received from RedRock authentication');
          }

          return token;
        } catch (error) {
          this.logger.error('Failed to authenticate with RedRock', error);
          throw new Error(
            `RedRock authentication failed: ${(error as Error).message}`,
          );
        }
      },
    );
  }

  private async findEntity(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    entityType: string,
    whereCriteria: Record<string, string>,
    idField: string,
  ): Promise<string | null> {
    try {
      const whereConditions = Object.entries(whereCriteria)
        .map(([key, value]) => `(${key} = '${value}')`)
        .join(' AND ');

      const params = new URLSearchParams();
      params.append('StoreID', storeId);
      params.append('Entity', entityType);
      params.append('WHERE', whereConditions);
      params.append('Field', idField);

      const response = await httpClient.get(`/V3/IPS?${params.toString()}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.data?.Data && response.data.Data.length > 0) {
        return response.data.Data[0][idField];
      }

      return null;
    } catch (_e) {
      return null;
    }
  }

  private async createEntity(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    entityType: string,
    fields: string[],
    responseIdField: string,
  ): Promise<string> {
    const payload: RedRockEntityPayload = {
      StoreID: storeId,
      Entity: entityType,
      Field: fields,
    };

    const response = await httpClient.post<RedRockEntityResponse>(
      '/V3/IPS',
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );

    const entityId = response.data[responseIdField];
    if (!entityId) {
      throw new Error(
        `Failed to create ${entityType}: No ${responseIdField} in response`,
      );
    }

    return entityId;
  }

  private async getOrCreatePrescriber(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    prescriber: PharmacyPrescriber,
    patientState: string,
  ): Promise<string> {
    const columnName = this.getClinicianColumnName(patientState);

    // 1. Check if doctor already has a RedRock clinician ID in the database for this store
    const doctor = await this.prisma.doctor.findUnique({
      where: { id: prescriber.id, npiNumber: prescriber.npi },
      select: {
        redrockStGeorgeClinicianId: true,
        redrockSpringvilleClinicianId: true,
      },
    });

    const existingStoreId = doctor?.[columnName];
    if (existingStoreId) return existingStoreId;

    // 2. Try to find existing prescriber by NPI
    const existingId = await this.findEntity(
      httpClient,
      token,
      storeId,
      'PRESCRIBER',
      { PRESCRIBERNPINUMBER: prescriber.npi },
      'PRESCRIBERID',
    );

    if (existingId) {
      // Update the doctor record with the RedRock clinician ID for this specific store
      await this.prisma.doctor.update({
        where: { id: prescriber.id },
        data: { [columnName]: existingId },
      });

      return existingId;
    }

    const fields: string[] = [
      `PRESCRIBERFIRSTNAME:${prescriber.firstName}`,
      `PRESCRIBERLASTNAME:${prescriber.lastName}`,
      `PRESCRIBERZIPCODE:19801`,
      `PRESCRIBERCITY:Wilmington`,
      `PRESCRIBERSTATE:DE`,
      `PRESCRIBERNPINUMBER:${prescriber.npi}`,
    ];

    if (prescriber.deaNumber) {
      fields.push(`PRESCRIBERDEALICENSE:${prescriber.deaNumber}`);
    }

    fields.push(`PRESCRIBEROFFICEPHONE1:(*************`);

    // 3. Create new prescriber and save the ID
    const newPrescriberId = await this.createEntity(
      httpClient,
      token,
      storeId,
      'PRESCRIBER',
      fields,
      'PRESCRIBERID',
    );

    // Update the doctor record with the new RedRock clinician ID for this specific store
    await this.prisma.doctor.update({
      where: { id: prescriber.id },
      data: { [columnName]: newPrescriberId },
    });

    this.logger.log(
      `Created new RedRock prescriber for ${prescriber.id} in ${columnName}: ${newPrescriberId}`,
    );

    return newPrescriberId;
  }

  private sanitizePhoneNumber(phoneNumber: string): string {
    // Remove all non-numeric characters from phone number
    return phoneNumber.replace(/\D/g, '');
  }

  private truncateZipcode(zipcode: string): string {
    // Extract only first 5 digits of zipcode, removing +4 extension
    const cleanZip = zipcode.replace(/\D/g, '');
    return cleanZip.substring(0, 5);
  }

  private async getOrCreatePatient(
    httpClient: AxiosInstance,
    token: string,
    storeId: string,
    patient: PharmacyPatient,
    facilityId: string,
    chargeAccountId: string,
  ): Promise<string> {
    const columnName = this.getPatientColumnName(patient.address.state);

    // 1. Check if patient already has a RedRock patient ID in the database for this store
    const patientRecord = await this.prisma.patient.findUnique({
      where: { id: patient.id },
      select: {
        redrockStGeorgePatientId: true,
        redrockSpringvillePatientId: true,
      },
    });

    const existingStoreId = patientRecord?.[columnName];
    if (existingStoreId) {
      this.logger.log(
        `Using existing RedRock patient ID for patient ${patient.id} in ${columnName}: ${existingStoreId}`,
      );
      return existingStoreId;
    }

    // 2. For patients without RedRock ID, create directly (no findEntity)
    const sanitizedPhone = this.sanitizePhoneNumber(patient.phoneNumber);
    const truncatedZipcode = this.truncateZipcode(patient.address.zipCode);

    const dob = formatInTimeZone(
      new Date(patient.dateOfBirth),
      'UTC',
      'yyyy/MM/dd',
    );

    const fields: string[] = [
      `PATIENTFIRSTNAME:${patient.firstName}`,
      `PATIENTLASTNAME:${patient.lastName}`,
      `PATIENTDOB:${dob}`,
      `PATIENTZIPCODE:${truncatedZipcode}`,
      `PATIENTCITY:${patient.address.city}`,
      `PATIENTSTATE:${patient.address.state}`,
      `PATIENTADDRESS:${patient.address.street1}${patient.address.street2 ? ' ' + patient.address.street2 : ''}`,
      `FACILITYID:${facilityId}`,
      `PATIENTDELIVERYFLAG:M`,
      `PATIENTCOURIERID:1`,
      `PRIMARYCHARGEACCOUNTID:${chargeAccountId}`,
      `PATIENTHOMEPHONE:${sanitizedPhone}`,
      `PATIENTEXTERNALID:${patient.id}`,
      `PATIENTACTIVE:Y`,
    ];

    if (patient.email) {
      fields.push(`PATIENTEMAIL:${patient.email}`);
    }

    if (patient.gender) {
      fields.push(`PATIENTGENDER:${patient.gender.toUpperCase()}`);
    }

    // Create new patient and save the ID
    const newPatientId = await this.createEntity(
      httpClient,
      token,
      storeId,
      'PATIENT',
      fields,
      'PATIENTID',
    );

    // Update the patient record with the new RedRock patient ID for this specific store
    await this.prisma.patient.update({
      where: { id: patient.id },
      data: { [columnName]: newPatientId },
    });

    this.logger.log(
      `Created new RedRock patient for ${patient.id} in ${columnName}: ${newPatientId}`,
    );

    return newPatientId;
  }

  private buildPrescriptionPayload(
    config: RedRockConfig,
    patientId: string,
    prescriberId: string,
    product: PharmacyPrescriptionProduct,
    request: PrescriptionRequest,
    prescriptionImageBase64: string,
  ): RedRockEntityPayload {
    // Convert prescription issue date to Mountain timezone
    const issueDate = request.prescriptionIssueDate || new Date();
    const formattedDate = formatInTimeZone(
      issueDate,
      'America/Denver',
      'yyyy/MM/dd',
    );

    const fields: string[] = [
      `PRESCRIPTIONTRANDATE:${formattedDate}`,
      `PATIENTID:${patientId}`,
      `PRESCRIBERID:${prescriberId}`,
      `DRUGID:${product.externalId}`,
      `PRESCRIPTIONFILLID:${product.externalId}`,
      `PRESCRIPTONORIGINALQTY:${product.quantity.toString()}`,
      `PRESCRIPTIONFILLQTY:${product.quantity.toString()}`,
      `PRESCRIPTIONSIGCODE:${product.sig}`,
      `PRESCRIPTIONDAYSSUPPLY:${(product.daysSupply || product.quantity * 28).toString()}`,
      `PRESCRIPTIONORIGINALREFILL:${(product.refills || 0).toString()}`,
      `PRESCRIPTIONRECEIVETHROUGH:4`,
      `PRESCRIPTIONTYPEID:O`,
      `PRESCRIPTIONBILL:Y`,
      `DELIVERYSCHEDULE:2`,
      `PRESCRIPTIONPACKINGCODE:6`,
      `PRESCRIPTIONEXTERNALRXID:RX-${request.treatmentId}-${product.id}`,
      `PRESCRIPTIONATTACHMENTBASE64:${prescriptionImageBase64}`,
      `PRESCRIPTIONNOTE:MEDICALLY NECESSARY - patient can't tolerate rapid dose titration and experiences muscle loss; compounding with glycine required to minimize muscle wasting; pharmacy to compound - bill to WILLOW ship to patient`,
    ];

    return {
      StoreID: config.storeId,
      Entity: 'PRESCRIPTION',
      Field: fields,
    };
  }

  private async logPharmacyIntegration(
    request: PrescriptionRequest,
    apiPayload: any,
    responseData: any,
    responseStatus: number,
    orderId: string | null,
  ): Promise<void> {
    try {
      // Get pharmacy ID from slug
      const pharmacy = await this.prisma.pharmacy.findFirst({
        where: { slug: 'redRock' },
      });

      if (!pharmacy) {
        this.logger.warn('Could not find RedRock pharmacy record for logging');
        return;
      }

      // Clone the payload to avoid modifying the original
      const logPayload = JSON.parse(JSON.stringify(apiPayload || {}));

      // Remove base64 image data from Field array for logging
      if (logPayload.Field && Array.isArray(logPayload.Field)) {
        logPayload.Field = logPayload.Field.map((field: string) => {
          if (field.startsWith('PRESCRIPTIONATTACHMENTBASE64:')) {
            return 'PRESCRIPTIONATTACHMENTBASE64:-';
          }
          return field;
        });
      }

      await this.prisma.pharmacyIntegration.create({
        data: {
          pharmacyId: pharmacy.id,
          prescriptionId: request.prescriptionId,
          orderId: orderId || 'NO_ORDER_ID',
          request: logPayload,
          response: responseData || {},
          responseStatus,
        },
      });
    } catch (error) {
      this.logger.error('Failed to log pharmacy integration', error);
      // Don't throw - logging failure shouldn't break the main flow
    }
  }
}
