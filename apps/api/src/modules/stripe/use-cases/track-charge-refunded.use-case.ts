import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { segmentTrackEvents } from '@modules/shared/events';
import { SegmentTrack } from '@modules/shared/types/events';
import { StripeService } from '@modules/stripe/service/stripe.service';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import Stripe from 'stripe';

@Injectable()
export class TrackChargeRefundedUseCase {
  private readonly logger = new Logger(TrackChargeRefundedUseCase.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly stripeService: StripeService,
    private readonly auditService: AuditService,
  ) {}

  async execute(data: Stripe.Charge) {
    const { customer } = data;
    const patient = await this.prismaService.patient.findFirst({
      where: { stripeCustomerId: String(customer) },
    });

    if (!patient) {
      this.logger.error(
        `Patient not found for Stripe customer: ${String(customer)}`,
      );
      return false;
    }

    let cardLast4 = '';
    let cardName = '';
    let productNames: string[] = [];
    let originalPaymentDate = '';

    // Get payment method details if available
    if (data.payment_method_details?.card) {
      cardLast4 = data.payment_method_details.card.last4 || '';
      // Get the card brand/type (Visa, Mastercard, Amex, etc.)
      cardName = data.payment_method_details.card.brand || '';
    }

    // Try to get more details from the payment intent or invoice
    if (data.payment_intent) {
      try {
        // If we have a payment intent ID, we can try to get more information
        const paymentIntent = await this.stripeService.retrievePaymentIntent(
          data.payment_intent as string,
        );

        // Get original payment date from created timestamp
        if (data.created) {
          originalPaymentDate = new Date(data.created * 1000).toISOString();
        }

        // If this is tied to an invoice, we can get product names
        const invoiceId = paymentIntent?.invoice as string;
        if (invoiceId) {
          const invoice =
            await this.stripeService.retrieveInvoiceWithLineItems(invoiceId);
          if (invoice && invoice.lines?.data) {
            productNames = invoice.lines.data
              .map((line) => {
                if (line.description) return line.description;
                if (line.price?.product) {
                  if (typeof line.price.product === 'string') {
                    return line.price.product; // Just the product ID
                  } else if (typeof line.price.product === 'object') {
                    return (line.price.product as Stripe.Product).name || '';
                  }
                }
                return '';
              })
              .filter((name) => name !== '');
          }
        }
      } catch (error) {
        this.logger.error(`Error getting payment details: ${error.message}`);
      }
    }

    const trackEvent: SegmentTrack = {
      event: segmentTrackEvents.chargeRefunded.name,
      userId: patient.id,
      properties: {
        value: `$${(data.amount_refunded / 100).toFixed(2)}`,
        chargeValue: data.amount / 100,
        refundedValue: `$${(data.amount_refunded / 100).toFixed(2)}`,
        capturedValue: data.amount_captured / 100,
        cardLast4,
        cardName,
        productNames,
        originalPaymentDate,
      },
    };

    this.logger.log(
      `Emitting refund event for patient ${patient.id} with amount ${data.amount_refunded / 100}`,
    );
    this.eventEmitter.emit(segmentTrackEvents.chargeRefunded.event, trackEvent);

    void this.auditService.append({
      patientId: patient.id,
      resourceType: 'PATIENT',
      resourceId: patient.id,
      action: 'PATIENT_REFUND_ISSUED',
      actorId: 'STRIPE',
      actorType: 'SYSTEM',
      details: {
        chargeValue: data.amount / 100,
        refundedValue: data.amount_refunded / 100,
        capturedValue: data.amount_captured / 100,
        cardLast4,
        cardName,
        productNames,
        originalPaymentDate,
      },
    });

    return true;
  }
}
