import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@/modules/shared/events';
import { SegmentIdentify, SegmentTrack } from '@/modules/shared/types/events';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import Stripe from 'stripe';

import { StripeService } from '../service/stripe.service';

@Injectable()
export class CustomerSaveNewPaymentMethodUseCase {
  private readonly logger = new Logger(
    CustomerSaveNewPaymentMethodUseCase.name,
  );

  constructor(
    private readonly prismaService: PrismaService,
    private readonly stripeService: StripeService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
    private readonly eventEmitter: EventEmitter2,
  ) {}
  async execute(
    patientId: string,
    paymentMethod: Stripe.PaymentMethod,
    options: { isDefault?: boolean } = {},
  ) {
    if (paymentMethod.type !== 'card') return;

    await runInDbTransaction(this.prismaService, async (prisma) => {
      if (options.isDefault) {
        await prisma.patientPaymentMethod.updateMany({
          where: {
            patientId,
          },
          data: {
            default: false,
          },
        });
      }

      const stripeCustomer = await this.stripeService.getCustomer(
        paymentMethod.customer as string,
      );

      if (!stripeCustomer) {
        this.logger.error(
          `Stripe customer with ID ${paymentMethod.customer} not found.`,
        );
        throw new Error('Stripe customer not found');
      }

      const billingAddress = {
        billingAddress1: stripeCustomer?.address?.line1,
        billingAddress2: stripeCustomer?.address?.line2,
        billingCity: stripeCustomer?.address?.city,
        billingState: stripeCustomer?.address?.state,
        billingZipcode: stripeCustomer?.address?.postal_code,
      };

      const paymentMethodAttachedIdentifyEvent: SegmentIdentify = {
        userId: patientId,
        traits: {
          cardExpiration: `${paymentMethod.card.exp_month}/${paymentMethod.card.exp_year}`,
          cardExpMonth: String(paymentMethod.card.exp_month),
          cardExpYear: String(paymentMethod.card.exp_year),
          cardLast4: paymentMethod.card.last4,
          ccardExpirationTime: new Date(
            paymentMethod.card.exp_year,
            paymentMethod.card.exp_month + 1,
            0,
          ).getTime(),
          cardType: paymentMethod.card.brand,
          ...billingAddress,
          paymentStatus: 'good',
        },
      };

      const billingUpdated: SegmentTrack = {
        event: segmentTrackEvents.billingUpdated.name,
        userId: patientId,
        properties: {
          cardExpiration: `${paymentMethod.card.exp_month}/${paymentMethod.card.exp_year}`,
          cardExpMonth: String(paymentMethod.card.exp_month),
          cardExpYear: String(paymentMethod.card.exp_year),
          cardLast4: paymentMethod.card.last4,
          ccardExpirationTime: new Date(
            paymentMethod.card.exp_year,
            paymentMethod.card.exp_month + 1,
            0,
          ).getTime(),
          cardType: paymentMethod.card.brand,
          ...billingAddress,
        },
      };

      await this.patientPaymentMethodPersistence.create(
        {
          patient: {
            connect: { id: patientId },
          },
          stripeId: paymentMethod.id,
          data: paymentMethod,
          type: paymentMethod.type,
          default: options.isDefault ?? false,
        },
        { prisma },
      );

      this.eventEmitter.emit(
        segmentIdentifyEvent.analyticIdentify,
        paymentMethodAttachedIdentifyEvent,
      );

      this.eventEmitter.emit(
        segmentTrackEvents.billingUpdated.event,
        billingUpdated,
      );
    });
  }
}
