import type { Stripe } from 'stripe';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { segmentTrackEvents } from '@/modules/shared/events';
import { SegmentTrack } from '@/modules/shared/types/events';
import { HttpException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class StripeChargeDisputeCreatedUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}
  async execute(event: Stripe.ChargeDisputeCreatedEvent, patientId: string) {
    const patient = await this.prismaService.patient.findFirst({
      where: { id: patientId },
    });

    if (!patient) {
      throw new HttpException('Patient not found', 404);
    }

    const chargeDisputeEvent: SegmentTrack = {
      event: segmentTrackEvents.disputeFiled.name,
      userId: patient.id,
      properties: {},
    };
    this.eventEmitter.emit(
      segmentTrackEvents.disputeFiled.event,
      chargeDisputeEvent,
    );
    return true;
  }
}
