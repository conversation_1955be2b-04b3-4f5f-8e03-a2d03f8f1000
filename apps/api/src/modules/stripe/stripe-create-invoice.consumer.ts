import { runInDbTransaction } from '@/helpers/transaction';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { subDays } from 'date-fns';

import { CacheService } from '../cache/cache.service';
import { PrismaService } from '../prisma/prisma.service';
import { StripeInvoiceUpdatedQueueEvent } from '../shared/events/invoice-topic.definition';
import { LoggerFactory, LoggerService } from '../shared/logger/logger.service';
import { DraftInvoice, StripeService } from './service/stripe.service';

@Injectable()
export class StripeCreateInvoiceConsumer {
  private readonly logger: LoggerService;

  constructor(
    private readonly stripe: StripeService,
    private cacheService: CacheService,
    private readonly prismaService: PrismaService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(
      StripeCreateInvoiceConsumer.name,
    );
  }

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'stripe-create-invoice',
    maxRetries: 3,
    filter: ['create'],
  })
  async createInvoice({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'create') return;

    const patientId = payload.patientId;
    const internalInvoiceId = payload.draftInvoice?.metadata
      ?.internalInvoiceId as string;

    const trottleKey = `stripe:invoice-create-trottle:patientId_${patientId}`;

    this.assertEveryTreatmentHasOnePrescription(payload.draftInvoice);
    await this.assertThrottleIsRespected(trottleKey, payload.draftInvoice);
    await this.assertPrescriptionsAreInvoicable(
      patientId,
      payload.draftInvoice,
    );
    await this.assertPatientAccountIsActive(patientId, internalInvoiceId);
    await this.assertInvoiceNotAlreadySent(payload.draftInvoice);

    if (payload.skipOldInvoiceCheck === true) {
      this.logger.warn(
        `[createInvoiceConsumer] Skipping old invoice check for draft ${payload.draftInvoice.metadata.internalInvoiceId}`,
        {
          patientId,
          internalInvoiceId,
        },
      );
    } else {
      await this.assertLastInvoiceIsOldEnough(patientId, payload.draftInvoice);
    }

    const { draftInvoice: draft } = payload;

    let invoiceId: string;
    try {
      const invoice = await this.stripe.createInvoice(draft.customerId, {
        description: draft.description,
        metadata: draft.metadata,
      });
      invoiceId = invoice.id;

      const prescriptionInvoiceItems: {
        prescriptionId: string;
        stripeInvoiceItemId: string;
        couponId?: string;
      }[] = [];
      for (const item of draft.items) {
        const invoiceItem = await this.stripe.addInvoiceItem({
          invoiceId: invoice.id,
          customerId: draft.customerId,
          item: item,
          couponId: item.couponId,
        });
        prescriptionInvoiceItems.push({
          prescriptionId: item.metadata.prescriptionId,
          stripeInvoiceItemId: invoiceItem.id,
        });
      }

      // apply coupons to the invoice
      const couponIds = draft.items
        .filter((item) => item.couponId)
        .map((item) => item.couponId);
      const invoiceCoupon = couponIds.length > 0 ? couponIds[0] : null;
      if (invoiceCoupon) {
        await this.stripe.applyCouponToInvoice(invoiceId, invoiceCoupon);
      }

      // finalize the invoice
      await runInDbTransaction(this.prismaService, async (prisma) => {
        for (const item of prescriptionInvoiceItems) {
          await prisma.prescription.update({
            where: { id: item.prescriptionId },
            data: {
              stripeInvoiceId: invoice.id,
              stripeInvoiceItemId: item.stripeInvoiceItemId,
              stripeCouponId: item.couponId,
              status: 'open',
            },
          });
        }

        // send the invoice to Stripe
        await this.stripe.attemptInvoiceCollect(invoiceId);
      });
    } catch (error) {
      this.logger.error(
        error,
        { patientId: payload.patientId, internalInvoiceId },
        `Error creating invoice ${internalInvoiceId}`,
      );
      if (invoiceId) {
        try {
          await this.stripe.deletDraft(invoiceId);
          await this.cacheService.del(trottleKey);
        } catch (deleteError) {
          this.logger.error(
            deleteError,
            {
              stripeInvoiceId: invoiceId,
              internalInvoiceId,
              patientId,
            },
            `Failed to void invoice after creation - internalInvoiceId: ${internalInvoiceId}`,
          );
        }
      }
      throw error;
    }
  }

  assertEveryTreatmentHasOnePrescription(draftInvoice: DraftInvoice) {
    if (
      draftInvoice.metadata.prescriptionIdList.length !==
      draftInvoice.metadata.treatmentIdList.length
    ) {
      this.logger.error(
        `Draft invoice ${draftInvoice.metadata.internalInvoiceId} every treatment must have one prescription`,
        {
          internalInvoiceId: draftInvoice.metadata.internalInvoiceId,
          prescriptionCount: draftInvoice.metadata.prescriptionIdList.length,
        },
      );
      throw new HttpException(
        `Draft invoice ${draftInvoice.metadata.internalInvoiceId} every treatment must have one prescription`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async assertThrottleIsRespected(trottleKey: string, draft: DraftInvoice) {
    const value = await this.cacheService.get(trottleKey);
    if (value) {
      this.logger.error(
        `Invoice creation throttled for draft ${draft.metadata.internalInvoiceId} - trottleValue: ${value}`,
        {
          internalInvoiceId: draft.metadata.internalInvoiceId,
          trottleValue: value,
        },
      );
      throw new HttpException(
        `Invoice creation throttled for draft ${draft.metadata.internalInvoiceId} - trottleValue: ${value}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    await this.cacheService.set(trottleKey, 'true', 30);
  }

  async assertPatientAccountIsActive(
    patientId: string,
    draftInvoiceId: string,
  ) {
    const patient = await this.prismaService.patient.findUnique({
      where: { id: patientId },
      select: { status: true },
    });

    if (!patient) {
      this.logger.error(
        `Patient NOT_FOUND for draft invoice ${draftInvoiceId}`,
        {
          patientId,
          internalInvoiceId: draftInvoiceId,
        },
      );
      throw new HttpException(
        `Patient NOT_FOUND for draft invoice ${draftInvoiceId}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const INACTIVE_STATUSES = [
      'banned',
      'cancelled',
      'deleted',
      'onboardingRejected',
      'pendingApprovalFromDoctor',
    ];

    if (INACTIVE_STATUSES.includes(patient.status)) {
      this.logger.error(
        `Patient NOT_ACTIVE for draft invoice ${draftInvoiceId} - status: ${patient.status}`,
        {
          patientId,
          status: patient.status,
          internalInvoiceId: draftInvoiceId,
        },
      );
      throw new HttpException(
        `Patient NOT_ACTIVE for draft invoice ${draftInvoiceId} - status: ${patient.status}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async assertInvoiceNotAlreadySent(draft: DraftInvoice) {
    const internalInvoiceId = draft.metadata.internalInvoiceId;
    const invoices = await this.stripe.listInvoices({
      customerId: draft.customerId,
      limit: 1,
      metadata: {
        internalInvoiceId: { equals: draft.metadata.internalInvoiceId },
      },
    });

    if (invoices.data.length > 0) {
      this.logger.error(`draft Invoice ${internalInvoiceId} already sent`, {
        customerId: draft.customerId,
        internalInvoiceId,
        foundInvoiceId: invoices.data[0].id,
      });
      throw new HttpException(
        `draft Invoice ${internalInvoiceId} already sent`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async assertLastInvoiceIsOldEnough(patientId: string, draft: DraftInvoice) {
    const internalInvoiceId = draft.metadata.internalInvoiceId;

    const prescriptions = await this.prismaService.prescription.findMany({
      where: {
        id: { in: draft.items.map((i) => i.metadata.prescriptionId) },
        createdAt: {
          gte: subDays(new Date(), 2),
        },
      },
      select: {
        id: true,
        createdAt: true,
      },
    });

    if (prescriptions.length === draft.items.length) {
      this.logger.warn(
        `[createInvoiceConsumer] skipping assertLastInvoiceIsOldEnough some prescriptions are too recent for draft ${internalInvoiceId}`,
        {
          patientId,
          internalInvoiceId,
          prescriptionCount: prescriptions.length,
        },
      );
      return;
    }

    const invoices = await this.stripe.listInvoices({
      customerId: draft.customerId,
      limit: 1,
    });

    if (invoices.data.length > 0) {
      const lastInvoice = invoices.data[0];
      const lastInvoiceDate = new Date(lastInvoice.created * 1000);

      if (['draft', 'uncollectible', 'void'].includes(lastInvoice.status)) {
        return;
      }

      const lastInvoicePriceIds = lastInvoice.lines.data
        .map((line) => line.price?.id)
        .filter((id) => !!id);

      if (lastInvoicePriceIds.length !== lastInvoice.lines.data.length) {
        this.logger.error(
          `Last invoice for draft ${internalInvoiceId} has missing price IDs`,
          {
            patientId,
            internalInvoiceId,
            foundInvoiceId: lastInvoice.id,
            lastInvoicePriceIds,
          },
        );
        throw new HttpException(
          `Last invoice for draft ${internalInvoiceId} has missing price IDs`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const lastInvoiceHasSimilarProductsToDraft = draft.items.some((item) =>
        lastInvoicePriceIds.includes(item.priceId),
      );

      const twentyDaysAgo = subDays(new Date(), 20);
      if (lastInvoiceDate.getTime() > twentyDaysAgo.getTime()) {
        if (lastInvoice.paid && lastInvoiceHasSimilarProductsToDraft) {
          this.logger.error(
            `Last invoice is too recent for draft ${internalInvoiceId}`,
            {
              patientId,
              internalInvoiceId,
              foundInvoiceId: lastInvoice.id,
            },
          );
          throw new HttpException(
            `Last invoice is too recent for draft ${internalInvoiceId}`,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        } else if (lastInvoice.status === 'open') {
          // handle new invoice to retry paiment
          const prescription = await this.prismaService.prescription.findFirst({
            where: { stripeInvoiceId: lastInvoice.id, status: 'failed' },
          });

          if (!prescription) {
            this.logger.error(
              `found recent invoice with no associated prescription - draftId: ${internalInvoiceId}`,
              {
                patientId,
                internalInvoiceId,
                invoiceId: lastInvoice.id,
              },
            );

            throw new HttpException(
              'found recent invoice for patient with no associated prescription',
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }

          // const lastOpenPrescription =
          //   await this.prismaService.prescription.findMany({
          //     where: {
          //       treatmentId: prescription.treatmentId,
          //       status: 'open',
          //       stripeInvoiceId: null,
          //     },
          //   });

          // if (lastOpenPrescription.length === 1) {
          //   return;
          // }

          // console.warn(
          //   `[createInvoiceConsumer] no lastOpenPrescription`,
          //   `patientId: ${prescription.patientId} - invoiceId: ${lastInvoice.id}`,
          // );
          // throw new Error(`[createInvoiceConsumer] no lastOpenPrescription`);
        }
      }
    }
  }

  async assertPrescriptionsAreInvoicable(
    patientId: string,
    draft: DraftInvoice,
  ) {
    const internalInvoiceId = draft.metadata.internalInvoiceId;

    const prescriptionIds = draft.items.map((i) => i.metadata.prescriptionId);

    const prescriptions = await this.prismaService.prescription.findMany({
      where: {
        id: { in: prescriptionIds },
      },
      select: {
        id: true,
        status: true,
        stripeInvoiceId: true,
        treatmentId: true,
        treatment: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    });

    if (prescriptionIds.length !== prescriptions.length) {
      this.logger.error(
        `Some prescriptions are not found for draft invoice ${draft.metadata.internalInvoiceId}`,
        {
          patientId,
          internalInvoiceId,
          prescriptionsInInvoice: prescriptionIds,
          prescriptionsInDatabase: prescriptions.map((p) => p.id),
        },
      );
      throw new HttpException(
        `Some prescriptions are not found for draft invoice ${draft.metadata.internalInvoiceId}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    for (const prescription of prescriptions) {
      if (prescription.status !== 'open') {
        this.logger.error(
          `Prescription ${prescription.id} status != open for draft invoice ${draft.metadata.internalInvoiceId}`,
          {
            patientId,
            internalInvoiceId,
            prescription: {
              id: prescription.id,
              status: prescription.status,
            },
          },
        );

        throw new HttpException(
          `Prescription ${prescription.id} status != open for draft invoice ${draft.metadata.internalInvoiceId}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      if (prescription.stripeInvoiceId) {
        this.logger.error(
          `Prescription ${prescription.id} already has an invoice for draft invoice ${draft.metadata.internalInvoiceId}`,
          {
            patientId,
            internalInvoiceId,
            prescription: {
              id: prescription.id,
              stripeInvoiceId: prescription.stripeInvoiceId,
            },
          },
        );

        throw new HttpException(
          `Prescription ${prescription.id} already has an invoice for draft invoice ${draft.metadata.internalInvoiceId}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      if (!prescription.treatment) {
        this.logger.error(
          `Prescription ${prescription.id} has no treatment for draft invoice ${draft.metadata.internalInvoiceId}`,
          {
            patientId,
            internalInvoiceId,
            prescription: {
              id: prescription.id,
              treatmentId: prescription.treatmentId,
            },
          },
        );
        throw new HttpException(
          `Prescription ${prescription.id} has no treatment for draft invoice ${draft.metadata.internalInvoiceId}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const treatmentStatusBlacklist = [
        'cancelled',
        'completed',
        'inProgress.waitingForPrescription',
      ];

      if (treatmentStatusBlacklist.includes(prescription.treatment.status)) {
        this.logger.error(
          `Treatment ${prescription.treatment.id} is not active for draft invoice ${draft.metadata.internalInvoiceId}`,
          {
            patientId,
            internalInvoiceId,
            prescription: {
              id: prescription.id,
              treatmentId: prescription.treatmentId,
              treatmentStatus: prescription.treatment.status,
            },
          },
        );
        throw new HttpException(
          `Treatment ${prescription.treatment.id} is not active for draft invoice ${draft.metadata.internalInvoiceId}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }
}
