import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { ChatModule } from '../chat/chat.module';
import { ContextModule } from '../context/context.module';
import { PrismaModule } from '../prisma/prisma.module';
import { IntercomService } from './intercom.service';
import { IntercomConversationEventUseCase } from './use-cases/intercom-conversation-event.use-case';
import { WebhooksController } from './webhooks.controller';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    forwardRef(() => ChatModule),
    ContextModule,
  ],
  providers: [IntercomService, IntercomConversationEventUseCase],
  controllers: [WebhooksController],
  exports: [IntercomService],
})
export class IntercomModule {}
