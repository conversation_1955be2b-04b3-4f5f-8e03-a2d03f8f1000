import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { DrizzleModule } from '@adapters/persistence/config/drizzle/drizzle.module';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { AuthService } from '@modules/auth/auth.service';
import { AppCacheModule } from '@modules/cache/cache.module';
import { SesModule } from '@modules/shared/aws/ses/ses.module';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';

import { ContextModule } from '../context/context.module';
import { PrismaModule } from '../prisma/prisma.module';
import { CognitoJwtStrategy } from './cognito.jwt-strategy';
import { CognitoService } from './cognito.service';
import { SuperAdminGuard } from './decorators/super-admin.decorator';
import { CapabilityGuard } from './guards/capability.guard';
import { JwtService } from './jwt.service';

@Module({
  imports: [
    JwtModule.register({ secret: 'willow!sAGr3atC0mP4ny' }),
    DrizzleModule,
    PrismaModule,
    forwardRef(() => TreatmentModule),
    AppCacheModule,
    SesModule,
    ContextModule,
  ],
  providers: [
    CognitoJwtStrategy,
    CognitoService,
    PatientPersistence,
    JwtService,
    PatientPaymentMethodPersistence,
    AuthService,
    SuperAdminGuard,
    CapabilityGuard,
  ],
  exports: [
    CognitoService,
    JwtService,
    AuthService,
    SuperAdminGuard,
    CapabilityGuard,
  ],
})
export class AuthModule {}
