import { runInDbTransaction } from '@/helpers/transaction';
import { segmentTrackEvents } from '@/modules/shared/events';
import { TreatmentUpdatedEvent } from '@/modules/shared/events/treatment-topic.definition';
import { SegmentTrack } from '@/modules/shared/types/events';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { subDays } from 'date-fns';

import { PrismaService } from '../prisma/prisma.service';
import { OrchestrationService } from '../shared/orchestration/orchestration.service';
import { TreatmentService } from './services/treatment.service';

@Injectable()
export class TreatmentUncollectibleWorker {
  private readonly logger = new Logger(TreatmentUncollectibleWorker.name);
  private isDisabled: boolean = true;

  constructor(
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly treatmentService: TreatmentService,
    private readonly orchestrationService: OrchestrationService,
  ) {
    // Check IS_CLI first, if true, disable this worker
    this.isDisabled = this.config.get('IS_CLI') === 'true';
    if (this.isDisabled) {
      this.logger.warn('TreatmentUncollectibleWorker is disabled in CLI mode');
    }
  }

  @Cron(CronExpression.EVERY_12_HOURS)
  async markFailedPrescriptionsAsUncollectible(): Promise<void> {
    if (this.isDisabled) {
      return;
    }

    this.logger.log('Starting uncollectible treatments check');

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'markFailedPrescriptionsAsUncollectible-cron',
          ttl: 1000 * 60 * 60 * 11, // 11 hours (less than 12 hour cron schedule)
          thisInstanceMustBePrimary: true,
        },
        async () => {
          const now = new Date();
          const thirtyDaysAgo = subDays(now, 40);

          await runInDbTransaction(this.prisma, async (prisma) => {
            // Find treatments that have been in failed status for more than 40 days
            const failedTreatments = await prisma.treatment.findMany({
              where: {
                status: 'failed',
                failedAt: { lte: thirtyDaysAgo },
              },
              include: {
                prescription: true,
                patient: {
                  include: {
                    user: true,
                    doctor: {
                      include: {
                        user: true,
                      },
                    },
                  },
                },
              },
              orderBy: { failedAt: 'desc' },
              take: 500,
            });

            this.logger.log(
              `Found ${failedTreatments.length} failed treatments older than 40 days`,
            );

            for (const treatment of failedTreatments) {
              try {
                // Find the latest prescription for this treatment
                const latestPrescription = treatment.prescription.sort(
                  (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
                )[0];

                if (!latestPrescription) {
                  this.logger.error(
                    `Treatment ${treatment.id} is in failed state but has no prescriptions. Skipping.`,
                  );
                  continue;
                }

                this.logger.log(
                  `Processing treatment ${treatment.id} with latest prescription ${latestPrescription.id} in status '${latestPrescription.status}'`,
                );

                // Handle based on prescription status
                if (latestPrescription.status === 'open') {
                  // Void open prescriptions
                  await prisma.prescription.update({
                    where: { id: latestPrescription.id },
                    data: {
                      status: 'voided',
                      lastError: {
                        message:
                          'Prescription voided due to treatment being uncollectible after 40 days',
                      },
                    },
                  });
                  this.logger.log(
                    `Voided open prescription ${latestPrescription.id} for treatment ${treatment.id}`,
                  );
                } else if (latestPrescription.status === 'failed') {
                  // Update failed prescriptions to uncollectible
                  await prisma.prescription.update({
                    where: { id: latestPrescription.id },
                    data: {
                      status: 'uncollectible',
                      lastError: {
                        message:
                          'Prescription marked as uncollectible after 40 days',
                      },
                    },
                  });
                  this.logger.log(
                    `Marked prescription ${latestPrescription.id} as uncollectible for treatment ${treatment.id}`,
                  );
                } else if (latestPrescription.status === 'voided') {
                  // Voided prescriptions don't need updates
                  this.logger.log(
                    `Prescription ${latestPrescription.id} for treatment ${treatment.id} is already voided`,
                  );
                } else {
                  // Unexpected status
                  this.logger.warn(
                    `Treatment ${treatment.id} has latest prescription ${latestPrescription.id} with unexpected status '${latestPrescription.status}'`,
                  );
                }

                // Process the treatment state machine
                const treatmentEventsToEmit: {
                  event: TreatmentUpdatedEvent['event'];
                }[] = [];

                const actor = await this.treatmentService.getActor(
                  treatment.id,
                  (e) => {
                    treatmentEventsToEmit.push(e);
                  },
                  undefined,
                  { prisma },
                );

                // Send uncollectible event to treatment if it's not already uncollectible
                const snapshot = actor.getSnapshot();
                if (
                  snapshot.value !== 'uncollectible' &&
                  snapshot.can({ type: 'uncollectible' })
                ) {
                  actor.send({ type: 'uncollectible' });
                  await this.treatmentService.updateTreatmentRecord(actor, {
                    prisma,
                  });

                  this.logger.log(
                    `Marked treatment ${treatment.id} as uncollectible`,
                  );
                }

                // Emit treatment updated events
                for (const { event } of treatmentEventsToEmit) {
                  await this.treatmentService.emitTreatmentUpdatedEvent(
                    event,
                    treatment.id,
                    { prisma },
                  );
                }

                // Update patient status to nonActivePrescription
                await prisma.patient.update({
                  where: { id: treatment.patientId },
                  data: { status: 'nonActivePrescription' },
                });

                // Emit tracking event
                const trackingEvent: SegmentTrack = {
                  event: segmentTrackEvents.prescriptionUncollectible.name,
                  userId: treatment.patient.userId,
                  properties: {},
                };

                this.eventEmitter.emit(
                  segmentTrackEvents.prescriptionUncollectible.event,
                  trackingEvent,
                );

                // Also emit segment identify event to update patient status
                this.eventEmitter.emit('segment.identify', {
                  userId: treatment.patient.userId,
                  traits: {
                    status: 'inactive',
                  },
                });

                this.logger.log(
                  `Successfully marked treatment ${treatment.id} as uncollectible and patient ${treatment.patientId} as nonActivePrescription`,
                );
              } catch (error) {
                this.logger.error(
                  `Error processing treatment ${treatment.id}:`,
                  error,
                );
              }
            }
          });
        },
      );
    } catch (error) {
      this.logger.error('Error in uncollectible treatments check:', error);
    }
  }
}
