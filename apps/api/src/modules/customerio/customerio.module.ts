import { ChatModule } from '@modules/chat/chat.module';
import { CustomerioController } from '@modules/customerio/customerioController';
import { CustomerioSendUseCase } from '@modules/customerio/use-cases/customerio-send.use-case';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { Module } from '@nestjs/common';

import { CustomerioService } from './customerio.service';

@Module({
  imports: [PrismaModule, ChatModule],
  providers: [CustomerioService, CustomerioSendUseCase],
  exports: [CustomerioService],
  controllers: [CustomerioController],
})
export class CustomerioModule {}
