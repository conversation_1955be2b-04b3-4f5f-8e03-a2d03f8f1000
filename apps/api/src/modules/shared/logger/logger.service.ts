import { ContextService } from '@/modules/context/context.service';
import { Injectable, Logger } from '@nestjs/common';

import { OrchestrationService } from '../orchestration/orchestration.service';

@Injectable()
export class LoggerService {
  private readonly logger: Logger;

  constructor(
    private readonly orchestrationService: OrchestrationService,
    private readonly contextService: ContextService,
    private readonly name: string,
  ) {
    this.logger = new Logger(name);
  }

  private enrichContext() {
    return {
      context: {
        name: this.name,
        instance: {
          id: this.orchestrationService.getInstanceId(),
          isPrimary: this.orchestrationService.isPrimaryInstance(),
        },
        actor: this.contextService.getActor(),
      },
    };
  }

  log(message: string, context: Record<string, unknown> = {}) {
    this.logger.log({ ...context, ...this.enrichContext() }, message);
  }

  error(
    error: Error | string,
    context: Record<string, unknown> = {},
    message?: string,
  ) {
    this.logger.error(
      { ...context, ...this.enrichContext(), err: error },
      message ?? (error instanceof Error ? error.message : error),
    );
  }

  warn(message: string, context: Record<string, unknown> = {}) {
    this.logger.warn({ ...context, ...this.enrichContext() }, message);
  }

  debug(message: string, context: Record<string, unknown> = {}) {
    this.logger.debug({ ...context, ...this.enrichContext() }, message);
  }

  verbose(message: string, context: Record<string, unknown> = {}) {
    this.logger.verbose({ ...context, ...this.enrichContext() }, message);
  }
}

@Injectable()
export class LoggerFactory {
  constructor(
    private readonly orchestrationService: OrchestrationService,
    private readonly contextService: ContextService,
  ) {}

  createLogger(name: string): LoggerService {
    return new LoggerService(
      this.orchestrationService,
      this.contextService,
      name,
    );
  }
}
