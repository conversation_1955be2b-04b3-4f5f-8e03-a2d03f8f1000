import * as fs from 'node:fs';
import * as path from 'node:path';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { ProductPriceService } from '@modules/product/services/product-price.service';
import { ProductService } from '@modules/product/services/product.service';
import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';
import { Command, CommandRunner, Option } from 'nest-commander';
import { Stripe } from 'stripe';

@Command({
  name: 'clean-slate-environment',
  description:
    'Create a clean slate environment with default users but syncing pharmacies, products and prices from production',
})
export class CleanSlateEnvironmentCommand extends CommandRunner {
  private prodPrisma: PrismaClient;
  private prodStripe: Stripe;
  private localStripe: Stripe;

  constructor(
    private readonly dosespotService: DosespotService,
    private readonly productService: ProductService,
    private readonly productPriceService: ProductPriceService,
    private readonly prisma: PrismaService,
  ) {
    super();
  }

  @Option({
    flags: '--skip-images',
    description:
      'Skip image processing (downloading and uploading) to improve performance',
  })
  parseSkipImages(): boolean {
    return true;
  }

  async run(
    passedParams: string[],
    options?: { skipImages?: boolean },
  ): Promise<void> {
    // Parse options
    const skipImageProcessing =
      passedParams.includes('--skip-images') ||
      passedParams.includes('-s') ||
      options?.skipImages;
    if (skipImageProcessing) {
      console.log('Image processing will be skipped for better performance');
    }

    const startTime = Date.now();
    let stepStartTime = Date.now();

    try {
      console.log('=== Starting clean slate environment creation ===');

      // Display important warning about database structure mismatches
      console.log('\n⚠️  IMPORTANT WARNING ⚠️');
      console.log('━'.repeat(80));
      console.log(
        'This command syncs data from production to the current environment.',
      );
      console.log(
        'Due to the dynamic nature of this project, there may be structural',
      );
      console.log(
        'differences between production and staging/local databases.',
      );
      console.log('');
      console.log(
        'If this command fails, it may require special fine-tuning to handle:',
      );
      console.log('• Schema differences between environments');
      console.log('• Missing or renamed database columns');
      console.log('• Changed foreign key relationships');
      console.log('• New or removed database constraints');
      console.log('');
      console.log(
        'Please review any errors carefully and adapt the command as needed.',
      );
      console.log('━'.repeat(80));
      console.log('');

      // Validate environment file exists
      console.log('\n[1/14] Validating environment file...');
      stepStartTime = Date.now();
      this.validateEnvironmentFile();
      console.log(
        `✅ Environment file validation completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Initialize connections to production environment
      console.log('\n[2/14] Initializing production connections...');
      stepStartTime = Date.now();
      this.initializeProductionConnections();
      console.log(
        `✅ Production connections initialized in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Delete data in the correct order
      console.log('\n[3/14] Deleting existing data...');
      stepStartTime = Date.now();
      await this.deleteData();
      console.log(
        `✅ Data deletion completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Archive existing Stripe products and prices
      console.log('\n[4/14] Archiving existing Stripe products and prices...');
      stepStartTime = Date.now();
      await this.archiveExistingStripeProducts();
      console.log(
        `✅ Stripe products and prices archived in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync states with production
      console.log('\n[5/14] Syncing states from production...');
      stepStartTime = Date.now();
      await this.syncStates();
      console.log(
        `✅ States sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync pharmacies with DoseSpot integration
      console.log('\n[6/14] Syncing pharmacies from production...');
      stepStartTime = Date.now();
      await this.syncPharmacies();
      console.log(
        `✅ Pharmacies sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync product categories from production
      console.log('\n[7/14] Syncing product categories from production...');
      stepStartTime = Date.now();
      await this.syncProductCategories();
      console.log(
        `✅ Product categories sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync products from production
      console.log('\n[8/14] Syncing products from production...');
      stepStartTime = Date.now();
      await this.syncProducts(skipImageProcessing);
      console.log(
        `✅ Products sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync product price equivalence groups from production
      console.log(
        '\n[9/14] Syncing product price equivalence groups from production...',
      );
      stepStartTime = Date.now();
      await this.syncProductPriceEquivalenceGroups();
      console.log(
        `✅ Product price equivalence groups sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync product prices from production
      console.log('\n[10/14] Syncing product prices from production...');
      stepStartTime = Date.now();
      await this.syncProductPrices();
      console.log(
        `✅ Product prices sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync specific coupons from production
      console.log('\n[11/14] Syncing specific coupons from production...');
      stepStartTime = Date.now();
      await this.syncSpecificCoupons();
      console.log(
        `✅ Coupons sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync questionnaires from production
      console.log('\n[12/14] Syncing questionnaires from production...');
      stepStartTime = Date.now();
      await this.syncQuestionnaires();
      console.log(
        `✅ Questionnaires sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync product price mappings from production
      console.log(
        '\n[13/14] Syncing product price mappings from production...',
      );
      stepStartTime = Date.now();
      await this.syncProductPriceMappings();
      console.log(
        `✅ Product price mappings sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      // Sync Stripe-only products from production
      console.log('\n[14/14] Syncing Stripe-only products from production...');
      stepStartTime = Date.now();
      await this.syncStripeOnlyProducts(skipImageProcessing);
      console.log(
        `✅ Stripe-only products sync completed in ${this.formatTime(Date.now() - stepStartTime)}`,
      );

      const totalTime = Date.now() - startTime;
      console.log(
        `\n=== Clean slate environment created successfully in ${this.formatTime(totalTime)} ===`,
      );
    } catch (error) {
      console.error(
        `Failed to create clean slate environment: ${error.message}`,
      );
      if (error.stack) {
        console.debug(error.stack);
      }
      process.exit(1);
    } finally {
      // Close production connections
      if (this.prodPrisma) {
        await this.prodPrisma.$disconnect();
      }
    }
  }

  private validateEnvironmentFile(): void {
    const envPath = path.resolve(process.cwd(), '../../.env.production');
    if (!fs.existsSync(envPath)) {
      throw new Error('.env.production file not found');
    }

    console.log('.env.production file found');
  }

  private initializeProductionConnections(): void {
    const envPath = path.resolve(process.cwd(), '../../.env.production');
    const envConfig = dotenv.parse(fs.readFileSync(envPath));
    const prodStripeKey = envConfig.STRIPE_SECRET_KEY;

    // Also get the local Stripe key for archiving existing products
    const localStripeKey = process.env.STRIPE_SECRET_KEY;
    if (!localStripeKey) {
      throw new Error(
        'STRIPE_SECRET_KEY not found in local environment variables',
      );
    }

    // Construct the database URL from individual environment variables
    const user = envConfig.POSTGRES_USER;
    const password = envConfig.POSTGRES_PASSWORD;
    const host = envConfig.POSTGRES_HOST;
    const port = envConfig.POSTGRES_PORT;
    const database = envConfig.POSTGRES_DB;

    if (!user || !password || !host || !port || !database) {
      throw new Error(
        'One or more required database configuration variables missing in .env.production',
      );
    }

    const prodDbUrl = `postgres://${user}:${password}@${host}:${port}/${database}`;

    if (!prodStripeKey) {
      throw new Error('STRIPE_SECRET_KEY not found in .env.production');
    }

    console.log(
      'Constructed production database URL from environment variables',
    );

    this.prodPrisma = new PrismaClient({
      datasources: {
        db: {
          url: prodDbUrl,
        },
      },
    });

    this.prodStripe = new Stripe(prodStripeKey, { apiVersion: '2023-10-16' });
    this.localStripe = new Stripe(localStripeKey, { apiVersion: '2023-10-16' });
    console.log('Production and local connections initialized successfully');
  }

  /**
   * Archive existing Stripe products and prices to avoid duplicates
   */
  private async archiveExistingStripeProducts(): Promise<void> {
    console.log('Archiving existing Stripe products and prices...');

    try {
      // Archive all existing products
      let productCount = 0;
      let hasMore = true;
      let startingAfter = undefined;

      while (hasMore) {
        const products = await this.localStripe.products.list({
          active: true,
          limit: 100,
          starting_after: startingAfter,
        });

        if (products.data.length === 0) {
          hasMore = false;
          break;
        }

        for (const product of products.data) {
          try {
            await this.localStripe.products.update(product.id, {
              active: false,
            });
            productCount++;
          } catch (error) {
            console.warn(
              `Failed to archive product ${product.id}: ${error.message}`,
            );
          }
        }

        hasMore = products.has_more;
        if (products.data.length > 0) {
          startingAfter = products.data[products.data.length - 1].id;
        }
      }

      console.log(`Archived ${productCount} products in Stripe`);
    } catch (error) {
      console.error(
        `Error archiving Stripe products and prices: ${error.message}`,
      );
      console.warn('Continuing with the sync despite archiving errors');
    }
  }

  /**
   * Delete data in the correct order to avoid foreign key constraint issues
   */
  private async deleteData(): Promise<void> {
    console.log('Deleting data in the correct order...');

    // Define the tables to be truncated in the correct order
    const tablesToClear = [
      'ConversationMessage',
      'ConversationWatcher',
      'ConversationRouter',
      'Conversation',
      'Outbox',
      'PatientFollowUp',
      'PharmacyIntegration',
      'Prescription',
      'Treatment',
      'PatientShippingAddress',
      'PatientPaymentMethod',
      'PatientDesiredTreatment',
      'ProductToProductCategory',
      'Subscription',
      'AuditLog',
      'DoctorAssignment',
      'ShipmentUpdate',
    ];

    // Get list of preserved users
    const preservedEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    // Preserve the IDs of users, doctors, and admins to keep
    const preservedUsers = await this.getPrismaClient().user.findMany({
      where: { email: { in: preservedEmails } },
      select: { id: true, email: true },
    });

    const preservedUserIds = preservedUsers.map((user) => user.id);
    console.log(
      `Preserving users: ${preservedUsers.map((u) => u.email).join(', ')}`,
    );

    // Get preserved doctors
    const preservedDoctors = await this.getPrismaClient().doctor.findMany({
      where: { userId: { in: preservedUserIds } },
      select: { id: true, userId: true },
    });

    const preservedDoctorIds = preservedDoctors.map((doc) => doc.id);

    // Get preserved admins
    const preservedAdmins = await this.getPrismaClient().admin.findMany({
      where: { userId: { in: preservedUserIds } },
      select: { id: true, userId: true },
    });

    const preservedAdminIds = preservedAdmins.map((admin) => admin.id);

    // Log preserved doctors on state
    const preservedDoctorsOnState =
      await this.getPrismaClient().doctorsOnState.findMany({
        where: { doctorId: { in: preservedDoctorIds } },
      });

    console.log(
      `Found ${preservedDoctorsOnState.length} doctor-state relationships to preserve`,
    );

    // Truncate each table in order
    for (const table of tablesToClear) {
      console.log(`Clearing table: ${table}`);
      await this.getPrismaClient().$executeRawUnsafe(
        `TRUNCATE TABLE "${table}" CASCADE;`,
      );
    }

    // Delete patients
    console.log('Deleting patients...');
    await this.getPrismaClient().patient.deleteMany({
      where: {},
    });

    // Delete product price mappings (depends on ProductPrice)
    console.log('Deleting product price mappings...');
    await this.getPrismaClient().productPriceMapping.deleteMany({
      where: {},
    });

    // Delete product prices (depends on Product and ProductPriceEquivalenceGroup)
    console.log('Deleting product prices...');
    await this.getPrismaClient().productPrice.deleteMany({
      where: {},
    });

    // Delete products (depends on Pharmacy and ProductCategory via ProductToProductCategory)
    console.log('Deleting products...');
    await this.getPrismaClient().product.deleteMany({
      where: {},
    });

    // Delete product categories
    console.log('Deleting product categories...');
    await this.getPrismaClient().productCategory.deleteMany({
      where: {},
    });

    // Delete product price equivalence groups (referenced by ProductPrice)
    console.log('Deleting product price equivalence groups...');
    await this.getPrismaClient().productPriceEquivalenceGroup.deleteMany({
      where: {},
    });

    // Delete pharmacy on state
    console.log('Deleting pharmacy on state...');
    await this.getPrismaClient().pharmacyOnState.deleteMany({
      where: {},
    });

    // Delete bulk transfers
    console.log('Deleting bulk transfers...');
    await this.getPrismaClient().bulkTransfer.deleteMany({
      where: {},
    });

    // Delete pharmacies
    console.log('Deleting pharmacies...');
    await this.getPrismaClient().pharmacy.deleteMany({
      where: {},
    });

    // Delete doctors on state except preserved ones
    console.log('Deleting doctors on state...');
    await this.getPrismaClient().doctorsOnState.deleteMany({
      where: {
        doctorId: {
          notIn: preservedDoctorIds,
        },
      },
    });

    // Delete doctors except preserved ones
    console.log('Deleting doctors...');
    await this.getPrismaClient().doctor.deleteMany({
      where: {
        id: {
          notIn: preservedDoctorIds,
        },
      },
    });

    // Delete referrals
    console.log('Deleting referrals...');
    await this.getPrismaClient().referral.deleteMany({
      where: {},
    });

    // Delete admins except preserved ones
    console.log('Deleting admins...');
    await this.getPrismaClient().admin.deleteMany({
      where: {
        id: {
          notIn: preservedAdminIds,
        },
      },
    });

    // Delete patient waiting list
    console.log('Deleting patient waiting list...');
    await this.getPrismaClient().patientWaitingList.deleteMany({
      where: {},
    });

    // Delete questionnaires
    console.log('Deleting questionnaires...');
    await this.getPrismaClient().questionnaire.deleteMany({
      where: {},
    });

    // Delete users except preserved ones
    console.log('Deleting users...');
    await this.getPrismaClient().user.deleteMany({
      where: {
        id: {
          notIn: preservedUserIds,
        },
      },
    });

    console.log('Data deletion completed');
  }

  /**
   * Helper method to get the current prisma client
   */
  private getPrismaClient(): PrismaClient {
    const { prisma } = this;
    // Use reflection to access private property if needed
    // @ts-expect-error accessing private property
    return prisma?._prisma || prisma;
  }

  /**
   * Sync states from production environment
   */
  private async syncStates(): Promise<void> {
    console.log('Syncing states from production...');

    // Get all states from production
    const prodStates = await this.prodPrisma.state.findMany();
    console.log(`Found ${prodStates.length} states in production`);

    // Get all states from local environment
    const localStates = await this.getPrismaClient().state.findMany();
    console.log(`Found ${localStates.length} states in local environment`);

    // Create a map of local states by code for quick lookups
    const localStatesByCode = new Map();
    localStates.forEach((state) => {
      localStatesByCode.set(state.code, state);
    });

    let updatedCount = 0;
    let newCount = 0;

    // Process each production state
    for (const prodState of prodStates) {
      const localState = localStatesByCode.get(prodState.code);

      if (localState) {
        // State exists locally, update enabled status if different
        if (localState.enabled !== prodState.enabled) {
          await this.getPrismaClient().state.update({
            where: { id: localState.id },
            data: { enabled: prodState.enabled },
          });
          updatedCount++;
        }
      } else {
        // State doesn't exist locally, create it
        await this.getPrismaClient().state.create({
          data: {
            name: prodState.name,
            code: prodState.code,
            enabled: prodState.enabled,
          },
        });
        newCount++;
      }
    }

    console.log(
      `Updated ${updatedCount} states and created ${newCount} states`,
    );
    console.log('States sync completed');
  }

  /**
   * Sync pharmacies from production and update DoseSpot pharmacy IDs
   */
  private async syncPharmacies(): Promise<void> {
    console.log('Syncing pharmacies from production...');

    // Get all pharmacies from production
    const prodPharmacies = await this.prodPrisma.pharmacy.findMany({
      include: {
        PharmacyOnState: {
          include: {
            state: true,
          },
        },
      },
    });

    console.log(`Found ${prodPharmacies.length} pharmacies in production`);

    // Get doctor for DoseSpot API calls - using super-doctor
    let doseSpotClinicianId: string | null = null;

    try {
      const superDoctor = await this.getPrismaClient().doctor.findFirst({
        where: {
          user: {
            email: '<EMAIL>',
          },
        },
        select: {
          doseSpotClinicianId: true,
        },
      });

      if (superDoctor?.doseSpotClinicianId) {
        doseSpotClinicianId = superDoctor.doseSpotClinicianId;
        console.log(
          `Using DoseSpot clinician ID: ${doseSpotClinicianId} from super-doctor`,
        );
      } else {
        // Try to find any doctor with a DoseSpot clinician ID
        const anyDoctor = await this.getPrismaClient().doctor.findFirst({
          where: {
            doseSpotClinicianId: { not: null },
          },
          select: {
            doseSpotClinicianId: true,
            user: {
              select: {
                email: true,
              },
            },
          },
        });

        if (anyDoctor?.doseSpotClinicianId) {
          doseSpotClinicianId = anyDoctor.doseSpotClinicianId;
          console.log(
            `Using DoseSpot clinician ID: ${doseSpotClinicianId} from doctor ${anyDoctor.user?.email}`,
          );
        }
      }
    } catch (error) {
      console.warn(
        `Error finding doctor with DoseSpot clinician ID: ${error.message}`,
      );
    }

    // If no DoseSpot clinician ID found, use a fallback value
    if (!doseSpotClinicianId) {
      doseSpotClinicianId = '3056356'; // Use the override value mentioned in memories
      console.warn(
        `No doctor with DoseSpot clinician ID found. Using fallback ID: ${doseSpotClinicianId}`,
      );
    }

    // Get a list of states
    const localStates = await this.getPrismaClient().state.findMany();
    const stateMap = new Map();
    localStates.forEach((state) => {
      stateMap.set(state.code, state);
    });

    // Set for tracking which pharmacy names we've already queried for fallback IDs
    const queriedPharmacyNames = new Set<string>();
    let cvsFallbackResults: any[] = [];

    for (const prodPharmacy of prodPharmacies) {
      console.log(`Processing pharmacy: ${prodPharmacy.name}`);

      // Search for pharmacy by name in DoseSpot
      let doseSpotPharmacyId: string | null = null;
      let searchSuccessful = false;

      try {
        // Search for pharmacy by name
        const searchResult = await this.dosespotService.searchPharmacies(
          prodPharmacy.name,
          doseSpotClinicianId,
        );

        if (searchResult.Items && searchResult.Items.length > 0) {
          // Always use the first result from the search
          doseSpotPharmacyId = searchResult.Items[0].PharmacyId.toString();
          console.log(
            `Found DoseSpot pharmacy ID: ${doseSpotPharmacyId} for ${prodPharmacy.name}`,
          );
          searchSuccessful = true;
        } else {
          // If no results, need to use CVS as fallback
          if (
            cvsFallbackResults.length === 0 &&
            !queriedPharmacyNames.has('cvs')
          ) {
            try {
              queriedPharmacyNames.add('cvs');
              const cvsSearchResult =
                await this.dosespotService.searchPharmacies(
                  'cvs',
                  doseSpotClinicianId,
                );

              if (cvsSearchResult.Items && cvsSearchResult.Items.length > 0) {
                cvsFallbackResults = cvsSearchResult.Items;
                console.log(
                  `Found ${cvsFallbackResults.length} CVS pharmacies as fallback options`,
                );
              } else {
                console.warn('No CVS pharmacies found for fallback');
              }
            } catch (cvsError) {
              console.warn(
                `Failed to search for CVS pharmacies: ${cvsError.message}`,
              );
            }
          }

          if (cvsFallbackResults.length > 0) {
            // Use a random CVS pharmacy (don't reuse the same one for all)
            const randomIndex = Math.floor(
              Math.random() * cvsFallbackResults.length,
            );
            const fallbackPharmacy = cvsFallbackResults[randomIndex];
            doseSpotPharmacyId = fallbackPharmacy.PharmacyId.toString();

            // Remove this pharmacy from the fallback options to avoid duplicates
            cvsFallbackResults.splice(randomIndex, 1);

            console.log(
              `Using CVS fallback for ${prodPharmacy.name}: DoseSpot ID ${doseSpotPharmacyId}`,
            );
            searchSuccessful = true;
          }
        }
      } catch (error) {
        console.warn(
          `Failed to find pharmacy for ${prodPharmacy.name}: ${error.message}`,
        );
      }

      // If all searches failed, use a hardcoded fallback ID
      if (!searchSuccessful || !doseSpotPharmacyId) {
        // Generate a random ID between 1000000 and 9999999 to avoid duplicates
        doseSpotPharmacyId = (
          Math.floor(Math.random() * 9000000) + 1000000
        ).toString();
        console.warn(
          `Using generated fallback DoseSpot ID: ${doseSpotPharmacyId} for ${prodPharmacy.name}`,
        );
      }

      // Create the pharmacy in local DB
      try {
        const newPharmacy = await this.getPrismaClient().pharmacy.create({
          data: {
            name: prodPharmacy.name,
            doseSpotPharmacyId: doseSpotPharmacyId,
            regularPriority: prodPharmacy.regularPriority,
            usingGLP1Priority: prodPharmacy.usingGLP1Priority,
            color: prodPharmacy.color,
            slug: prodPharmacy.slug,
            enabled: prodPharmacy.enabled,
            enableApi: prodPharmacy.enableApi || false,
            metadata: prodPharmacy.metadata || {},
          },
        });

        // Create pharmacy-state relationships
        for (const stateRelation of prodPharmacy.PharmacyOnState) {
          const localState = stateMap.get(stateRelation.state.code);
          if (!localState) {
            console.warn(
              `State ${stateRelation.state.code} not found locally, skipping pharmacy-state relation`,
            );
            continue;
          }

          await this.getPrismaClient().pharmacyOnState.create({
            data: {
              pharmacyId: newPharmacy.id,
              stateId: localState.id,
            },
          });
        }

        console.log(
          `Created pharmacy: ${newPharmacy.name} with ID ${newPharmacy.id}`,
        );
      } catch (error) {
        console.error(
          `Failed to create pharmacy ${prodPharmacy.name}: ${error.message}`,
        );
      }
    }

    console.log('Pharmacies sync completed');
  }

  /**
   * Sync products from production
   * @param skipImageProcessing If true, skip image processing to improve performance
   */
  private async syncProducts(
    skipImageProcessing: boolean = false,
  ): Promise<void> {
    console.log('Syncing products from production...');

    // Get all active products from production with their pharmacy details and categories
    const prodProducts = await this.prodPrisma.product.findMany({
      where: {
        active: true,
      },
      include: {
        pharmacy: {
          select: {
            id: true,
            name: true,
            regularPriority: true,
            usingGLP1Priority: true,
          },
        },
        productCategories: {
          include: {
            productCategory: true,
          },
        },
      },
    });

    console.log(`Found ${prodProducts.length} active products in production`);

    // Get local pharmacies for mapping
    const localPharmacies = await this.getPrismaClient().pharmacy.findMany();

    // Create a map for pharmacy name to local pharmacy object
    const pharmacyByName = new Map();

    localPharmacies.forEach((pharmacy) => {
      // Map by name (lowercase for case-insensitive matching)
      pharmacyByName.set(pharmacy.name.toLowerCase(), pharmacy);
    });

    // Get local product categories for mapping
    const localCategories =
      await this.getPrismaClient().productCategory.findMany();

    // Create a map for category name to local category object
    const categoryByName = new Map();

    localCategories.forEach((category) => {
      // Map by name (case-insensitive)
      categoryByName.set(category.name.toLowerCase(), category);
    });

    console.log(
      `Found ${localCategories.length} local product categories for mapping`,
    );

    // Default pharmacy (highest priority) to use if no match is found
    const defaultPharmacy =
      await this.getPrismaClient().pharmacy.findFirstOrThrow({
        orderBy: {
          regularPriority: 'desc',
        },
      });

    console.log(
      `Using default pharmacy ${defaultPharmacy.name} if no match is found`,
    );

    let createdCount = 0;
    let skippedCount = 0;

    // Group products by pharmacy priority
    const pharmacyGroups = new Map<number, Array<any>>();

    // First, group products by their pharmacy priority
    for (const prodProduct of prodProducts) {
      const pharmacyPriority = prodProduct.pharmacy?.regularPriority || 0;

      if (!pharmacyGroups.has(pharmacyPriority)) {
        pharmacyGroups.set(pharmacyPriority, []);
      }

      pharmacyGroups.get(pharmacyPriority).push(prodProduct);
    }

    // Sort pharmacy priorities (descending - highest priority last)
    const sortedPriorities = Array.from(pharmacyGroups.keys()).sort(
      (a, b) => a - b,
    );

    // Process each pharmacy group in priority order (lowest to highest)
    for (const priority of sortedPriorities) {
      const productsInPharmacy = pharmacyGroups.get(priority);
      console.log(
        `Processing ${productsInPharmacy.length} products for pharmacy priority ${priority}...`,
      );

      // Sort products within this pharmacy by their order (descending - create in reverse order)
      productsInPharmacy.sort((a, b) => {
        const orderA = a.order || 0;
        const orderB = b.order || 0;
        return orderB - orderA;
      });

      // Process each product in order
      for (const prodProduct of productsInPharmacy) {
        try {
          console.log(
            `Processing product: ${prodProduct.name} (order: ${prodProduct.order || 0})`,
          );

          // Find the corresponding local pharmacy by name
          let localPharmacy = null;

          if (prodProduct.pharmacy?.name) {
            const pharmacyName = prodProduct.pharmacy.name.toLowerCase();
            localPharmacy = pharmacyByName.get(pharmacyName);

            if (localPharmacy) {
              console.log(
                `Found matching pharmacy by name: ${localPharmacy.name} for product ${prodProduct.name}`,
              );
            }
          }

          // If no matching pharmacy found, use default
          if (!localPharmacy) {
            localPharmacy = defaultPharmacy;
            console.warn(
              `No matching pharmacy found for product ${prodProduct.name}, using default pharmacy ${defaultPharmacy.name}`,
            );
          }

          // First check if we need to upload the image to Stripe
          let imageUrl = prodProduct.image;
          if (imageUrl && !skipImageProcessing) {
            try {
              // Upload the image using our helper method
              const uploadResult = await this.uploadImageToStripe(
                prodProduct.id,
                imageUrl,
              );

              // If we have a new image URL from the upload, use it
              if (uploadResult && uploadResult !== imageUrl) {
                console.log(`Uploaded product image for ${prodProduct.name}`);
                imageUrl = uploadResult;
              }
            } catch (error) {
              console.warn(
                `Failed to upload image for product ${prodProduct.name}: ${error.message}`,
              );
              // Continue with the original URL if upload fails
            }
          } else if (skipImageProcessing && imageUrl) {
            console.debug(
              `Skipping image processing for product ${prodProduct.name} as requested`,
            );
          }

          // Map production category names to local category IDs
          const localCategoryIds = [];
          for (const prodCategoryRelation of prodProduct.productCategories) {
            const categoryName = prodCategoryRelation.productCategory.name;
            const localCategory = categoryByName.get(
              categoryName.toLowerCase(),
            );

            if (localCategory) {
              localCategoryIds.push(localCategory.id);
              console.log(
                `Mapped category "${categoryName}" to local ID ${localCategory.id}`,
              );
            } else {
              console.warn(
                `Category "${categoryName}" not found in local database for product ${prodProduct.name}`,
              );
            }
          }

          // Create the product with the same metadata
          const createProductDto = {
            name: prodProduct.name,
            description: prodProduct.description,
            image: skipImageProcessing ? null : imageUrl,
            pharmacyId: localPharmacy.id,
            isCore: prodProduct.isCore,
            active: prodProduct.active,
            isAvailableInOnboarding: prodProduct.isAvailableInOnboarding,
            form: prodProduct.form,
            tags: prodProduct.tags,
            type: prodProduct.type,
            label: prodProduct.label,
            onboardingLabel: prodProduct.onboardingLabel,
            order: prodProduct.order,
            notice: prodProduct.notice,
            supplyLength: prodProduct.supplyLength,
            weightLossMultiplier: prodProduct.weightLossMultiplier,
            customCard: prodProduct.customCard,
            genericName: prodProduct.genericName,
            metadata: prodProduct.metadata,
            productCategoryIds: localCategoryIds,
          };

          // Create the product using the product service
          const newProduct = await this.productService.create(createProductDto);
          console.log(
            `Created product: ${newProduct.name} with ID ${newProduct.id}`,
          );

          // Compare and sync metadata from production
          await this.syncProductMetadata(prodProduct.metadata, newProduct);

          createdCount++;
        } catch (error) {
          console.error(
            `Failed to create product ${prodProduct.name}: ${error.message}`,
          );
          skippedCount++;
        }
      }
    }

    console.log(
      `Created ${createdCount} products, skipped ${skippedCount} products`,
    );
    console.log('Products sync completed');
  }

  /**
   * Compare and sync metadata from production product to newly created product
   * Adds any missing keys from production metadata to the new product
   */
  private async syncProductMetadata(
    prodMetadata: any,
    newProduct: any,
  ): Promise<void> {
    try {
      // Get production metadata (passed as argument)
      const productionMetadata = prodMetadata || {};

      // Fetch the current metadata from the newly created product in the database
      // since the service doesn't return metadata in its response
      const currentProduct = await this.getPrismaClient().product.findUnique({
        where: { id: newProduct.id },
        select: { metadata: true },
      });

      if (!currentProduct) {
        console.error(
          `Could not find newly created product ${newProduct.id} to sync metadata`,
        );
        return;
      }

      const currentMetadata = currentProduct.metadata || {};

      // Check if there are any missing keys in the new product
      const missingKeys: string[] = [];
      const updatedMetadata = { ...currentMetadata };

      for (const [key, value] of Object.entries(productionMetadata)) {
        if (!(key in currentMetadata)) {
          missingKeys.push(key);
          updatedMetadata[key] = value;
        }
      }

      // If there are missing keys, update the product
      if (missingKeys.length > 0) {
        console.log(
          `Syncing ${missingKeys.length} missing metadata keys for product ${newProduct.name}: ${missingKeys.join(', ')}`,
        );

        await this.getPrismaClient().product.update({
          where: { id: newProduct.id },
          data: { metadata: updatedMetadata },
        });

        console.log(
          `Successfully synced metadata for product ${newProduct.name}`,
        );
      }
    } catch (error) {
      console.error(
        `Failed to sync metadata for product ${newProduct.name}: ${error.message}`,
      );
      // Don't throw error to avoid breaking the entire sync process
    }
  }

  /**
   * Sync Stripe-only products from production
   * Gets all active products from production Stripe, compares with production database,
   * and recreates any products that exist only in Stripe
   */
  private async syncStripeOnlyProducts(
    skipImageProcessing: boolean = false,
  ): Promise<void> {
    console.log('Syncing Stripe-only products from production...');

    try {
      // Get all active products from production Stripe
      console.log('Fetching all active products from production Stripe...');
      const prodStripeProducts = await this.getAllActiveStripeProducts();
      console.log(
        `Found ${prodStripeProducts.length} active products in production Stripe`,
      );

      // Get all active products from production database
      console.log('Fetching all active products from production database...');
      const prodDbProducts = await this.prodPrisma.product.findMany({
        where: { active: true },
        select: { id: true, name: true },
      });
      console.log(
        `Found ${prodDbProducts.length} active products in production database`,
      );

      // Create a set of product IDs that exist in the production database
      const dbProductIds = new Set(prodDbProducts.map((product) => product.id));

      // Find products that exist in Stripe but not in the database
      const stripeOnlyProducts = prodStripeProducts.filter(
        (stripeProduct) => !dbProductIds.has(stripeProduct.id),
      );

      console.log(
        `Found ${stripeOnlyProducts.length} products that exist only in production Stripe`,
      );

      if (stripeOnlyProducts.length === 0) {
        console.log('No Stripe-only products to sync');
        return;
      }

      // Log the Stripe-only products for visibility
      console.log('Stripe-only products to recreate:');
      stripeOnlyProducts.forEach((product) => {
        console.log(`  - ${product.name} (${product.id})`);
      });

      let createdCount = 0;
      let skippedCount = 0;

      // Process each Stripe-only product
      for (const stripeProduct of stripeOnlyProducts) {
        try {
          console.log(`Processing Stripe-only product: ${stripeProduct.name}`);

          // Handle image processing
          let imageUrl = stripeProduct.images?.[0];
          if (imageUrl && !skipImageProcessing) {
            try {
              const uploadResult = await this.uploadImageToStripe(
                stripeProduct.id,
                imageUrl,
              );
              if (uploadResult && uploadResult !== imageUrl) {
                console.log(`Uploaded product image for ${stripeProduct.name}`);
                imageUrl = uploadResult;
              }
            } catch (error) {
              console.warn(
                `Failed to upload image for product ${stripeProduct.name}: ${error.message}`,
              );
            }
          } else if (skipImageProcessing && imageUrl) {
            console.debug(
              `Skipping image processing for product ${stripeProduct.name} as requested`,
            );
          }

          // Create the product directly in local Stripe (no database record)
          const stripeProductData: any = {
            name: stripeProduct.name,
            description: stripeProduct.description || '',
            active: stripeProduct.active,
            metadata: stripeProduct.metadata || {},
          };

          // Add images if available
          if (imageUrl && !skipImageProcessing) {
            stripeProductData.images = [imageUrl];
          }

          // Create the product in local Stripe
          const newStripeProduct =
            await this.localStripe.products.create(stripeProductData);

          console.log(
            `Created Stripe-only product in local Stripe: ${newStripeProduct.name} with ID ${newStripeProduct.id}`,
          );

          // Get and recreate active prices for this product
          await this.recreateStripeProductPrices(
            stripeProduct.id,
            newStripeProduct.id,
          );

          createdCount++;
        } catch (error) {
          console.error(
            `Failed to create Stripe-only product ${stripeProduct.name}: ${error.message}`,
          );
          skippedCount++;
        }
      }

      console.log(
        `Stripe-only products sync completed: ${createdCount} created, ${skippedCount} skipped`,
      );
    } catch (error) {
      console.error(`Error syncing Stripe-only products: ${error.message}`);
      console.warn('Continuing despite Stripe-only products sync errors');
    }
  }

  /**
   * Recreate active prices for a Stripe-only product
   */
  private async recreateStripeProductPrices(
    originalProductId: string,
    newProductId: string,
  ): Promise<void> {
    try {
      console.log(
        `Fetching active prices for product ${originalProductId} from production Stripe...`,
      );

      // Get all active prices for the original product from production Stripe
      const prodPrices = await this.prodStripe.prices.list({
        product: originalProductId,
        active: true,
        limit: 100,
      });

      if (prodPrices.data.length === 0) {
        console.log(`No active prices found for product ${originalProductId}`);
        return;
      }

      console.log(
        `Found ${prodPrices.data.length} active prices for product ${originalProductId}`,
      );

      let createdPricesCount = 0;
      let skippedPricesCount = 0;

      // Recreate each price in local Stripe
      for (const prodPrice of prodPrices.data) {
        try {
          console.log(
            `Creating price: ${prodPrice.nickname || prodPrice.id} (${prodPrice.unit_amount / 100} ${prodPrice.currency})`,
          );

          // Prepare price data for creation
          const priceData: any = {
            product: newProductId,
            unit_amount: prodPrice.unit_amount,
            currency: prodPrice.currency,
            active: prodPrice.active,
          };

          // Add optional fields if they exist
          if (prodPrice.nickname) {
            priceData.nickname = prodPrice.nickname;
          }

          if (
            prodPrice.metadata &&
            Object.keys(prodPrice.metadata).length > 0
          ) {
            priceData.metadata = prodPrice.metadata;
          }

          if (prodPrice.tax_behavior) {
            priceData.tax_behavior = prodPrice.tax_behavior;
          }

          // Create the price in local Stripe
          const newPrice = await this.localStripe.prices.create(priceData);

          console.log(
            `Created price in local Stripe: ${newPrice.nickname || newPrice.id} with ID ${newPrice.id}`,
          );

          createdPricesCount++;
        } catch (error) {
          console.error(
            `Failed to create price ${prodPrice.nickname || prodPrice.id}: ${error.message}`,
          );
          skippedPricesCount++;
        }
      }

      console.log(
        `Price recreation completed for product ${newProductId}: ${createdPricesCount} created, ${skippedPricesCount} skipped`,
      );
    } catch (error) {
      console.error(
        `Error recreating prices for product ${originalProductId}: ${error.message}`,
      );
      // Don't throw error to avoid breaking the entire product sync process
    }
  }

  /**
   * Get all active products from production Stripe
   */
  private async getAllActiveStripeProducts(): Promise<any[]> {
    const allProducts: any[] = [];
    let hasMore = true;
    let startingAfter: string | undefined = undefined;

    while (hasMore) {
      const products = await this.prodStripe.products.list({
        active: true,
        limit: 100,
        starting_after: startingAfter,
      });

      if (products.data.length === 0) {
        hasMore = false;
        break;
      }

      allProducts.push(...products.data);

      hasMore = products.has_more;
      if (products.data.length > 0) {
        startingAfter = products.data[products.data.length - 1].id;
      }
    }

    return allProducts;
  }

  /**
   * Upload an image from a URL to Stripe
   * Checks if the image already exists in S3 before downloading and uploading
   */
  private async uploadImageToStripe(
    productId: string,
    imageUrl: string,
  ): Promise<string> {
    // If the URL is already a Stripe URL, return it as is
    if (imageUrl && imageUrl.includes('stripe.com')) {
      console.debug(
        `Image for product ${productId} is already a Stripe URL, skipping upload`,
      );
      return imageUrl;
    }

    // Check if the image already exists in S3
    const bucket = process.env.AWS_S3_PRODUCTS_PHOTOS_BUCKETNAME;
    if (bucket && imageUrl) {
      try {
        const { S3 } = await import('aws-sdk');
        const s3 = new S3();
        const key = `${productId}.png`;

        try {
          // Check if the image already exists in S3
          await s3.headObject({ Bucket: bucket, Key: key }).promise();

          // Image exists in S3, construct the URL
          const baseUrl = process.env.CLOUDFRONT_PRODUCTS_PHOTOS_URL;
          const s3Url = baseUrl
            ? `${baseUrl}/${key}`
            : `https://${bucket}.s3.amazonaws.com/${key}`;

          console.log(
            `Image for product ${productId} already exists in S3, using existing URL`,
          );
          return s3Url;
        } catch (s3Error) {
          // NotFound errors are expected when the object doesn't exist yet
          if (s3Error.code !== 'NotFound') {
            console.warn(
              `Unexpected error checking S3 for product ${productId} image:`,
              s3Error,
            );
          }
          // Continue with Stripe upload if image doesn't exist in S3
        }
      } catch (error) {
        console.warn(`Error checking S3 for existing image: ${error.message}`);
        // Continue with Stripe upload if there's an error checking S3
      }
    }

    try {
      const startTime = Date.now();
      const { default: axios } = await import('axios');

      // Fetch the image from the URL
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
      });
      const buffer = Buffer.from(response.data, 'binary');
      const downloadTime = Date.now() - startTime;
      console.debug(
        `Downloaded image for product ${productId} in ${downloadTime}ms`,
      );

      // Determine the file type
      const contentType = response.headers['content-type'] || 'image/jpeg';

      // Upload to Stripe
      const uploadStartTime = Date.now();
      const uploadedFile = await this.prodStripe.files.create({
        // @ts-expect-error 'product_image' is not in the defined types but is valid for Stripe
        purpose: 'product_image',
        file: {
          data: buffer,
          name: `${productId}.${contentType.split('/')[1] || 'jpg'}`,
          type: contentType,
        },
      });
      const uploadTime = Date.now() - uploadStartTime;

      // Create a file link to make it publicly accessible
      const fileLink = await this.prodStripe.fileLinks.create({
        file: uploadedFile.id,
      });

      console.log(
        `Uploaded image for product ${productId} to Stripe in ${uploadTime}ms`,
      );
      return fileLink.url;
    } catch (error) {
      console.error(`Failed to upload image to Stripe: ${error.message}`);
      return imageUrl; // Return original URL if upload fails
    }
  }

  /**
   * Sync product prices from production
   */
  private async syncProductPrices(): Promise<void> {
    console.log('Syncing product prices from production...');

    // Get all active products from local db to map to prices
    const localProducts = await this.getPrismaClient().product.findMany({
      where: {
        active: true,
      },
    });

    // Create a map of local products by name for quick lookups
    const localProductsByName = new Map();
    localProducts.forEach((product) => {
      localProductsByName.set(product.name, product);
    });

    // Get all local equivalence groups for mapping
    const localEquivalenceGroups =
      await this.getPrismaClient().productPriceEquivalenceGroup.findMany();

    // Create a map of local equivalence groups by name
    const localEquivalenceGroupsByName = new Map();
    localEquivalenceGroups.forEach((group) => {
      if (group.name) {
        localEquivalenceGroupsByName.set(group.name.toLowerCase(), group);
      }
    });

    console.log(
      `Found ${localProducts.length} local products to sync prices for`,
    );
    console.log(
      `Found ${localEquivalenceGroups.length} local equivalence groups for mapping`,
    );

    try {
      // Try with Prisma models first
      await this.syncProductPricesWithPrisma(
        localProductsByName,
        localEquivalenceGroupsByName,
      );
    } catch (error) {
      console.error(
        `Failed to sync product prices with Prisma: ${error.message}`,
      );
    }

    console.log('Product prices sync completed');
  }

  /**
   * Use Prisma models to sync product prices - may fail if schemas differ
   */
  private async syncProductPricesWithPrisma(
    localProductsByName: Map<string, any>,
    localEquivalenceGroupsByName: Map<string, any>,
  ): Promise<void> {
    // Get all active prices from production
    const prodPrices = await this.prodPrisma.productPrice.findMany({
      where: {
        active: true,
      },
      include: {
        product: true,
        equivalenceGroup: true,
      },
    });

    console.log(`Found ${prodPrices.length} active prices in production`);

    let createdCount = 0;
    let skippedCount = 0;
    let defaultPriceCount = 0;

    // Track which products were processed to set default prices afterward
    const defaultPricesByProduct: Record<string, string> = {};

    // Group prices by product name
    const productPrices = new Map<string, Array<any>>();

    for (const prodPrice of prodPrices) {
      const productName = prodPrice.product.name;

      if (!productPrices.has(productName)) {
        productPrices.set(productName, []);
      }

      productPrices.get(productName).push(prodPrice);
    }

    // Process each product's prices
    for (const [productName, prices] of productPrices.entries()) {
      const localProduct = localProductsByName.get(productName);

      if (!localProduct) {
        console.warn(
          `No matching local product found for prices of product ${productName}`,
        );
        skippedCount += prices.length;
        continue;
      }

      console.log(
        `Processing ${prices.length} prices for product ${productName}...`,
      );

      // Sort prices by phase (descending - create in reverse order)
      prices.sort((a, b) => {
        const phaseA = a.phase || 0;
        const phaseB = b.phase || 0;
        return phaseB - phaseA;
      });

      // Process each price in order
      for (const prodPrice of prices) {
        try {
          console.log(
            `Processing price: ${prodPrice.name} (phase: ${prodPrice.phase || 0})`,
          );

          // Map production equivalence group to local ID by name
          let localEquivalenceGroupId = null;
          if (prodPrice.equivalenceGroupId && prodPrice.equivalenceGroup) {
            const groupName = prodPrice.equivalenceGroup.name;
            if (groupName) {
              const localGroup = localEquivalenceGroupsByName.get(
                groupName.toLowerCase(),
              );
              if (localGroup) {
                localEquivalenceGroupId = localGroup.id;
                console.log(
                  `Mapped equivalence group "${groupName}" to local ID ${localGroup.id}`,
                );
              } else {
                console.warn(
                  `Equivalence group "${groupName}" not found in local database for price ${prodPrice.name}`,
                );
              }
            }
          }

          // Create the price using the service
          const createPriceDto = {
            name: prodPrice.name,
            productId: localProduct.id,
            unit_amount: prodPrice.unit_amount,
            active: prodPrice.active,
            description: prodPrice.description,
            dosageDescription: prodPrice.dosageDescription,
            dosageLabel: prodPrice.dosageLabel,
            dosageTimeframe: prodPrice.dosageTimeframe,
            dosageAdditionalMessage: prodPrice.dosageAdditionalMessage,
            label: prodPrice.label,
            milligrams: prodPrice.milligrams,
            phase: prodPrice.phase,
            isDefaultPrice: false, // We'll set default prices after all prices are created
            equivalenceGroupId: localEquivalenceGroupId,
            compoundName: prodPrice.compoundName,
            patientDirections: prodPrice.patientDirections,
            additiveBenefit: prodPrice.additiveBenefit,
          };

          const newPrice =
            await this.productPriceService.create(createPriceDto);
          console.log(
            `Created price: ${newPrice.name} for product ${localProduct.name}`,
          );
          createdCount++;

          // Track if this price is the default price for the product
          if (prodPrice.product.defaultPriceId === prodPrice.id) {
            defaultPricesByProduct[localProduct.id] = newPrice.id;
          }
        } catch (error) {
          console.error(
            `Failed to create price ${prodPrice.name}: ${error.message}`,
          );
          skippedCount++;
        }
      }
    }

    // Set default prices for products
    for (const productId of Object.keys(defaultPricesByProduct)) {
      const priceId = defaultPricesByProduct[productId];
      try {
        await this.getPrismaClient().product.update({
          where: { id: productId },
          data: { defaultPriceId: priceId },
        });
        defaultPriceCount++;
      } catch (error) {
        console.error(
          `Failed to set default price for product ${productId}: ${error.message}`,
        );
      }
    }

    console.log(
      `Created ${createdCount} prices, skipped ${skippedCount} prices, set ${defaultPriceCount} default prices`,
    );
  }

  /**
   * Synchronize specific coupons from production to local environment
   * This method deletes all existing coupons in the local environment first,
   * then creates copies of the specified coupons from production.
   */
  private async syncSpecificCoupons(): Promise<void> {
    console.log('Syncing specific coupons from production...');

    // Coupon IDs to sync from production
    const couponIdsToSync = ['yN7yQ6jE', 'yRp9VvoQ', 'aYROqZgJ', 'mGPUx2NL'];
    console.log(
      `Will sync the following coupons: ${couponIdsToSync.join(', ')}`,
    );

    try {
      // Step 1: Delete all existing coupons in local environment
      console.log('Deleting all existing coupons in local environment...');
      let hasMore = true;
      let totalDeleted = 0;

      // List all coupons and delete them in batches
      while (hasMore) {
        const existingCoupons = await this.localStripe.coupons.list({
          limit: 100,
        });

        if (existingCoupons.data.length === 0) {
          hasMore = false;
          break;
        }

        for (const coupon of existingCoupons.data) {
          try {
            await this.localStripe.coupons.del(coupon.id);
            totalDeleted++;
            console.log(
              `Deleted coupon: ${coupon.id} - ${coupon.name || 'Unnamed'}`,
            );
          } catch (error) {
            console.warn(
              `Failed to delete coupon ${coupon.id}: ${error.message}`,
            );
          }
        }

        hasMore = existingCoupons.has_more;
      }

      console.log(`Deleted ${totalDeleted} existing coupons`);

      // Step 2: Get and create specified coupons from production
      let createdCount = 0;
      let skippedCount = 0;

      for (const couponId of couponIdsToSync) {
        try {
          // Retrieve the coupon from production
          const prodCoupon = await this.prodStripe.coupons.retrieve(couponId);
          console.log(
            `Retrieved coupon from production: ${prodCoupon.id} - ${prodCoupon.name || 'Unnamed'}`,
          );

          // Extract the coupon details for creation
          const couponData: any = {
            // Common required fields
            id: prodCoupon.id,
            name: prodCoupon.name,
            duration: prodCoupon.duration,
          };

          // Add optional fields based on the coupon type
          if (prodCoupon.percent_off !== null) {
            couponData.percent_off = prodCoupon.percent_off;
          } else if (prodCoupon.amount_off !== null) {
            couponData.amount_off = prodCoupon.amount_off;
            couponData.currency = prodCoupon.currency;
          }

          // Add duration-specific fields
          if (
            prodCoupon.duration === 'repeating' &&
            prodCoupon.duration_in_months
          ) {
            couponData.duration_in_months = prodCoupon.duration_in_months;
          }

          // Add other optional fields if they exist
          if (prodCoupon.max_redemptions) {
            couponData.max_redemptions = prodCoupon.max_redemptions;
          }

          if (prodCoupon.redeem_by) {
            couponData.redeem_by = prodCoupon.redeem_by;
          }

          // Create the coupon in local environment
          await this.localStripe.coupons.create(couponData);
          console.log(
            `Created coupon in local environment: ${prodCoupon.id} - ${prodCoupon.name || 'Unnamed'}`,
          );
          createdCount++;
        } catch (error) {
          console.error(`Failed to sync coupon ${couponId}: ${error.message}`);
          skippedCount++;
        }
      }

      console.log(
        `Successfully created ${createdCount} coupons, skipped ${skippedCount} coupons`,
      );
    } catch (error) {
      console.error(`Error syncing coupons: ${error.message}`);
      throw error;
    }
  }

  /**
   * Sync product price mappings from production
   */
  /**
   * Helper function to create consistent mapping keys
   */
  private createProductPriceMappingKey(
    pharmacyName: string,
    productName: string,
    priceName: string,
  ): string {
    return `${pharmacyName.toLowerCase()}|${productName}|${priceName}`;
  }

  private async syncProductPriceMappings(): Promise<void> {
    console.log('Syncing product price mappings from production...');

    try {
      // Get all product price mappings from production with necessary relations
      const prodMappings = await this.prodPrisma.productPriceMapping.findMany({
        select: {
          id: true,
          externalId: true,
          name: true,
          metadata: true,
          productPrice: {
            select: {
              name: true,
              product: {
                select: {
                  name: true,
                  pharmacy: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      console.log(
        `Found ${prodMappings.length} product price mappings in production`,
      );

      if (prodMappings.length === 0) {
        console.log('No product price mappings to sync');
        return;
      }

      // Get all local product prices with their products and pharmacies for mapping
      const localProductPrices =
        await this.getPrismaClient().productPrice.findMany({
          select: {
            id: true,
            name: true,
            product: {
              select: { name: true, pharmacy: { select: { name: true } } },
            },
          },
        });

      console.log(
        `Found ${localProductPrices.length} local product prices for mapping`,
      );

      // Type for local product price mapping
      type LocalProductPriceInfo = {
        id: string;
        name: string;
        product: { name: string; pharmacy: { name: string } };
      };

      // Create a map of local product prices by pharmacy name + product name + price name
      const localProductPriceMap = new Map<string, LocalProductPriceInfo>();

      localProductPrices.forEach((productPrice) => {
        const key = this.createProductPriceMappingKey(
          productPrice.product.pharmacy.name,
          productPrice.product.name,
          productPrice.name,
        );
        localProductPriceMap.set(key, productPrice);
      });

      // Delete existing product price mappings
      const deletedCount =
        await this.getPrismaClient().productPriceMapping.deleteMany({});
      console.log(
        `Deleted ${deletedCount.count} existing product price mappings`,
      );

      let createdCount = 0;
      let skippedCount = 0;
      const mappingsToCreate: Array<{
        id: string;
        externalId: string;
        productPriceId: string;
        name: string | null;
        metadata: any;
      }> = [];

      // Process each production mapping
      for (const prodMapping of prodMappings) {
        try {
          const prodPrice = prodMapping.productPrice;
          const prodProduct = prodPrice.product;
          const prodPharmacy = prodProduct.pharmacy;

          // Validate required fields
          if (!prodPharmacy?.name || !prodProduct?.name || !prodPrice?.name) {
            console.warn(
              `Skipping mapping ${prodMapping.id}: Missing required fields (pharmacy/product/price name)`,
            );
            skippedCount++;
            continue;
          }

          // only sync Red Rock mappings for storeId 11
          if (
            prodPharmacy.name === 'Red Rock' &&
            prodMapping.metadata['storeId'] !== '11'
          )
            continue;

          // Create key for lookup
          const key = this.createProductPriceMappingKey(
            prodPharmacy.name,
            prodProduct.name,
            prodPrice.name,
          );

          // Find the corresponding local product price
          const localProductPrice = localProductPriceMap.get(key);

          if (!localProductPrice) {
            console.warn(
              `Skipping mapping: No matching local product price found for ${prodPharmacy.name} - ${prodProduct.name} - ${prodPrice.name}`,
            );
            skippedCount++;
            continue;
          }

          // Prepare the mapping data
          mappingsToCreate.push({
            id: prodMapping.id, // Preserve the original ID
            externalId:
              prodPharmacy.name === 'Red Rock'
                ? '25387'
                : prodMapping.externalId,
            productPriceId: localProductPrice.id,
            name: prodMapping.name,
            metadata: prodMapping.metadata || undefined,
          });

          if (mappingsToCreate.length % 100 === 0) {
            console.log(`Prepared ${mappingsToCreate.length} mappings...`);
          }
        } catch (error) {
          console.error(
            `Failed to process product price mapping ${prodMapping.id}: ${error.message}`,
          );
          skippedCount++;
        }
      }

      // Create all mappings in batch
      if (mappingsToCreate.length > 0) {
        console.log(
          `Creating ${mappingsToCreate.length} product price mappings...`,
        );
        const result =
          await this.getPrismaClient().productPriceMapping.createMany({
            data: mappingsToCreate,
            skipDuplicates: true,
          });
        createdCount = result.count;
      }

      console.log(
        `✅ Synced product price mappings: ${createdCount} created, ${skippedCount} skipped`,
      );
    } catch (error) {
      console.error(`Error syncing product price mappings: ${error.message}`);
      console.warn('Continuing despite product price mapping sync errors');
    }
  }

  /**
   * Sync questionnaires from production
   */
  private async syncQuestionnaires(): Promise<void> {
    console.log('Syncing questionnaires from production...');

    try {
      // Get all questionnaires from production
      const prodQuestionnaires = await this.prodPrisma.questionnaire.findMany();
      console.log(
        `Found ${prodQuestionnaires.length} questionnaires in production`,
      );

      // Get all questionnaires from local environment
      const localQuestionnaires =
        await this.getPrismaClient().questionnaire.findMany();
      console.log(
        `Found ${localQuestionnaires.length} questionnaires in local environment`,
      );

      // Create a map of local questionnaires by type and version for quick lookups
      const localQuestionnaireMap = new Map<string, any>();
      localQuestionnaires.forEach((q) => {
        const key = `${q.type}_${q.version}`;
        localQuestionnaireMap.set(key, q);
      });

      let createdCount = 0;
      let updatedCount = 0;
      let skippedCount = 0;

      // Process each production questionnaire
      for (const prodQuestionnaire of prodQuestionnaires) {
        try {
          const key = `${prodQuestionnaire.type}_${prodQuestionnaire.version}`;
          const localQuestionnaire = localQuestionnaireMap.get(key);

          if (localQuestionnaire) {
            // Questionnaire exists locally, update it if needed
            console.log(
              `Updating questionnaire: ${prodQuestionnaire.name} (${prodQuestionnaire.type} v${prodQuestionnaire.version})`,
            );

            await this.getPrismaClient().questionnaire.update({
              where: { id: localQuestionnaire.id },
              data: {
                name: prodQuestionnaire.name,
                description: prodQuestionnaire.description,
                config: prodQuestionnaire.config,
              },
            });

            updatedCount++;
            console.log(
              `Updated questionnaire: ${prodQuestionnaire.name} (${prodQuestionnaire.type} v${prodQuestionnaire.version})`,
            );
          } else {
            // Questionnaire doesn't exist locally, create it
            console.log(
              `Creating questionnaire: ${prodQuestionnaire.name} (${prodQuestionnaire.type} v${prodQuestionnaire.version})`,
            );

            await this.getPrismaClient().questionnaire.create({
              data: {
                id: prodQuestionnaire.id, // Use the same ID to maintain references
                type: prodQuestionnaire.type,
                version: prodQuestionnaire.version,
                name: prodQuestionnaire.name,
                description: prodQuestionnaire.description,
                config: prodQuestionnaire.config,
                createdAt: prodQuestionnaire.createdAt,
              },
            });

            createdCount++;
            console.log(
              `Created questionnaire: ${prodQuestionnaire.name} (${prodQuestionnaire.type} v${prodQuestionnaire.version})`,
            );
          }
        } catch (error) {
          console.error(
            `Failed to sync questionnaire ${prodQuestionnaire.name}: ${error.message}`,
          );
          skippedCount++;
        }
      }

      console.log(
        `Synced questionnaires: ${createdCount} created, ${updatedCount} updated, ${skippedCount} skipped`,
      );
    } catch (error) {
      console.error(`Error syncing questionnaires: ${error.message}`);
      throw error;
    }
  }

  /**
   * Sync product categories from production
   */
  private async syncProductCategories(): Promise<void> {
    console.log('Syncing product categories from production...');

    try {
      // Get all product categories from production
      const prodCategories = await this.prodPrisma.productCategory.findMany();
      console.log(
        `Found ${prodCategories.length} product categories in production`,
      );

      // Delete existing categories (already done in deleteData, but just to be safe)
      await this.getPrismaClient().productCategory.deleteMany({});

      let createdCount = 0;
      let skippedCount = 0;

      // Recreate each category
      for (const prodCategory of prodCategories) {
        try {
          await this.getPrismaClient().productCategory.create({
            data: {
              id: prodCategory.id, // Preserve the same ID for references
              name: prodCategory.name,
              form: prodCategory.form,
              label: prodCategory.label,
              tags: prodCategory.tags,
              shortDescription: prodCategory.shortDescription,
              description: prodCategory.description,
              image: prodCategory.image,
              customCard: prodCategory.customCard,
              enabled: prodCategory.enabled,
              createdAt: prodCategory.createdAt,
              updatedAt: prodCategory.updatedAt,
            },
          });
          createdCount++;
          console.log(`Created product category: ${prodCategory.name}`);
        } catch (error) {
          console.error(
            `Failed to create product category ${prodCategory.name}: ${error.message}`,
          );
          skippedCount++;
        }
      }

      console.log(
        `Product categories sync completed: ${createdCount} created, ${skippedCount} skipped`,
      );
    } catch (error) {
      console.error(`Error syncing product categories: ${error.message}`);
      throw error;
    }
  }

  /**
   * Sync product price equivalence groups from production
   */
  private async syncProductPriceEquivalenceGroups(): Promise<void> {
    console.log('Syncing product price equivalence groups from production...');

    try {
      // Get all equivalence groups from production
      const prodGroups =
        await this.prodPrisma.productPriceEquivalenceGroup.findMany();
      console.log(
        `Found ${prodGroups.length} product price equivalence groups in production`,
      );

      // Delete existing groups (already done in deleteData, but just to be safe)
      await this.getPrismaClient().productPriceEquivalenceGroup.deleteMany({});

      let createdCount = 0;
      let skippedCount = 0;

      // Recreate each group
      for (const prodGroup of prodGroups) {
        try {
          await this.getPrismaClient().productPriceEquivalenceGroup.create({
            data: {
              id: prodGroup.id, // Preserve the same ID for references
              name: prodGroup.name,
            },
          });
          createdCount++;
          console.log(
            `Created product price equivalence group: ${prodGroup.name || prodGroup.id}`,
          );
        } catch (error) {
          console.error(
            `Failed to create product price equivalence group ${prodGroup.name || prodGroup.id}: ${error.message}`,
          );
          skippedCount++;
        }
      }

      console.log(
        `Product price equivalence groups sync completed: ${createdCount} created, ${skippedCount} skipped`,
      );
    } catch (error) {
      console.error(
        `Error syncing product price equivalence groups: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Format a time duration in milliseconds to a human-readable string
   */
  private formatTime(ms: number): string {
    if (ms < 1000) {
      return `${ms}ms`;
    } else if (ms < 60000) {
      const seconds = Math.floor(ms / 1000);
      const remainingMs = ms % 1000;
      return `${seconds}s ${remainingMs}ms`;
    } else {
      const minutes = Math.floor(ms / 60000);
      const seconds = Math.floor((ms % 60000) / 1000);
      return `${minutes}m ${seconds}s`;
    }
  }
}
