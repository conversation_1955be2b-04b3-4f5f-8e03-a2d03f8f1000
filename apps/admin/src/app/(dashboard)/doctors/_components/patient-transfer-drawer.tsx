'use client';

import { useEffect, useMemo, useState } from 'react';
import { format } from 'date-fns';
import { XIcon } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import { Calendar } from '@willow/ui/base/calendar';
import { Checkbox } from '@willow/ui/base/checkbox';
import { Label } from '@willow/ui/base/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@willow/ui/base/popover';
import { toast } from '@willow/ui/base/use-toast';
import { Drawer, DrawerContent, DrawerOverlay } from '@willow/ui/base/vaul';
import { Loader } from '@willow/ui/loader';

import {
  useDeleteBulkTransfer,
  useGetAllActiveDoctors,
  useTransferPatients,
  useUpdateBulkTransfer,
} from '~/hooks/doctors';
import { useGetAllEnabledStates } from '~/hooks/states';

export interface Doctor {
  totalPatientCount?: number;
  id: string;
  active?: boolean;
  user: {
    firstName: string;
    lastName: string;
    email: string;
  };
  prescribesIn: {
    state: {
      id: string;
      code: string;
      name: string;
    };
  }[];
  statePatientCounts?: {
    stateId: string;
    stateName: string;
    stateCode: string;
    patientCount: number;
    enabled: boolean;
    prescribesIn?: boolean;
  }[];
}

export interface State {
  id: string;
  code: string;
  name: string;
  enabled: boolean;
}

interface PatientTransferDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  sourceDoctor: Doctor | null;
  mode: 'transfer' | 'outOfOffice';
}

interface TransferRuleProps {
  index: number;
  availableDoctors: Doctor[];
  availableStates: State[];
  selectedStates: string[];
  onSelectedStatesChange: (states: string[]) => void;
  selectedDoctors: string[];
  onSelectedDoctorsChange: (doctors: string[]) => void;
  isEditing: boolean;
  onSave: () => void;
  onCancel: () => void;
  onDelete: () => void;
  sourceDoctor: Doctor | null;
}

const TransferRule = ({
  index,
  availableDoctors,
  availableStates,
  selectedStates,
  onSelectedStatesChange,
  selectedDoctors,
  onSelectedDoctorsChange,
  isEditing,
  onSave,
  onCancel,
  onDelete,
  sourceDoctor,
}: TransferRuleProps) => {
  // State to track whether we are filtering by doctors or states
  const [filterMode, setFilterMode] = useState<'states' | 'doctors'>('states');

  // Helper function to check if doctor prescribes in a state
  const doctorPrescribesInState = (stateId: string) => {
    // First check if the backend provides the prescribesIn field directly on the state
    const stateData = sourceDoctor?.statePatientCounts?.find(
      (s) => s.stateId === stateId,
    );
    if (stateData && typeof stateData.prescribesIn === 'boolean') {
      return stateData.prescribesIn;
    }
    // Fallback to checking the prescribesIn relation
    return (
      sourceDoctor?.prescribesIn?.some((p) => p.state.id === stateId) ?? false
    );
  };

  // Filter doctors based on selected states
  const eligibleDoctors = useMemo(() => {
    // First filter out the source doctor
    const filteredDoctors = (availableDoctors || []).filter(
      (doctor) => doctor.id !== sourceDoctor?.id,
    );

    if (selectedStates.length === 0) return filteredDoctors;

    return filteredDoctors.filter((doctor) => {
      // Check if doctor can prescribe in all selected states
      return selectedStates.every((stateId) =>
        doctor.prescribesIn.some(
          (prescribesIn) => prescribesIn.state.id === stateId,
        ),
      );
    });
  }, [availableDoctors, selectedStates, sourceDoctor?.id]);

  // Filter states based on selected doctors
  const eligibleStates = useMemo(() => {
    if (selectedDoctors.length === 0) return availableStates;

    // Get all states where the selected doctors can prescribe
    const statesByDoctor = selectedDoctors.map((doctorId) => {
      const doctor = availableDoctors.find((d) => d.id === doctorId);
      if (!doctor) return [];
      return doctor.prescribesIn.map((p) => p.state.id);
    });

    // Only keep states that all selected doctors can prescribe in
    const commonStateIds = statesByDoctor.reduce((common, doctorStates) => {
      if (common.length === 0) return doctorStates;
      return common.filter((stateId) => doctorStates.includes(stateId));
    }, [] as string[]);

    // Filter available states to only include common states
    return availableStates.filter((state) => commonStateIds.includes(state.id));
  }, [availableStates, availableDoctors, selectedDoctors]);

  // Calculate total patients for the selected states
  const totalPatientsCount = useMemo(() => {
    if (!sourceDoctor?.statePatientCounts) return 0;

    return (
      sourceDoctor.statePatientCounts
        .filter(
          (stateCount) =>
            selectedStates.includes(stateCount.stateId) && stateCount.enabled,
        )
        .reduce((sum, stateCount) => sum + stateCount.patientCount, 0) || 0
    );
  }, [sourceDoctor, selectedStates]);

  // Calculate per-doctor distribution for multiple target doctors
  const patientsPerDoctor = useMemo(() => {
    if (selectedDoctors.length === 0) return {};

    // For even distribution among selected doctors
    const baseCount = Math.floor(totalPatientsCount / selectedDoctors.length);
    const remainder = totalPatientsCount % selectedDoctors.length;

    return selectedDoctors.reduce(
      (acc, doctorId, index) => {
        // Add one extra to the first 'remainder' doctors to distribute remainder evenly
        acc[doctorId] = baseCount + (index < remainder ? 1 : 0);
        return acc;
      },
      {} as Record<string, number>,
    );
  }, [totalPatientsCount, selectedDoctors]);

  // Handle the tab change
  const handleTabChange = (mode: 'doctors' | 'states') => {
    setFilterMode(mode);
  };

  if (isEditing) {
    return (
      <div className="mb-6 w-full overflow-hidden rounded-md border border-gray-200 p-4">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-md font-medium">Transfer Rule {index + 1}</h3>
        </div>

        {/* Tabs for switching between doctors-first and states-first views */}
        <div className="mb-4 border-b">
          <div className="flex">
            <button
              className={`mr-4 pb-2 text-sm font-medium ${
                filterMode === 'states'
                  ? 'border-b-2 border-denim text-denim'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => handleTabChange('states')}
            >
              Select States First
            </button>
            <button
              className={`pb-2 text-sm font-medium ${
                filterMode === 'doctors'
                  ? 'border-b-2 border-denim text-denim'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => handleTabChange('doctors')}
            >
              Select Doctors First
            </button>
          </div>
        </div>

        {filterMode === 'states' ? (
          <>
            {/* States Selection First */}
            <div className="mb-4">
              <div className="mb-2 flex items-center justify-between">
                <h4 className="text-sm font-medium">Select States:</h4>
                {availableStates.length > 0 && (
                  <button
                    className="cursor-pointer text-sm font-medium text-orange hover:underline"
                    onClick={() => {
                      if (selectedStates.length === availableStates.length) {
                        onSelectedStatesChange([]);
                      } else {
                        onSelectedStatesChange(
                          availableStates.map((state) => state.id),
                        );
                      }
                    }}
                  >
                    {selectedStates.length === availableStates.length
                      ? 'Clear All'
                      : 'Select All'}
                  </button>
                )}
              </div>
              <div className="grid max-w-full grid-cols-2 gap-2 sm:grid-cols-3">
                {[...availableStates]
                  .sort((a, b) => a.name.localeCompare(b.name))
                  .map((state) => {
                    // Find patient count for this state
                    const patientCount =
                      sourceDoctor?.statePatientCounts?.find(
                        (s) => s.stateId === state.id,
                      )?.patientCount || 0;

                    const prescribesInState = doctorPrescribesInState(state.id);

                    return (
                      <div
                        key={state.id}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`state-${state.id}`}
                          checked={selectedStates.includes(state.id)}
                          className="border-gray-300 data-[state=unchecked]:bg-white"
                          onCheckedChange={(checked) => {
                            if (checked) {
                              onSelectedStatesChange([
                                ...selectedStates,
                                state.id,
                              ]);
                            } else {
                              onSelectedStatesChange(
                                selectedStates.filter((id) => id !== state.id),
                              );
                            }
                          }}
                        />
                        <Label
                          htmlFor={`state-${state.id}`}
                          className={`flex cursor-pointer items-center text-sm font-normal ${
                            !prescribesInState ? 'text-orange-600' : ''
                          }`}
                          title={
                            !prescribesInState
                              ? 'Doctor has patients but no longer prescribes in this state'
                              : ''
                          }
                        >
                          {state.name}
                          {!prescribesInState && (
                            <span className="ml-1 text-orange-600">!</span>
                          )}
                          <span className="ml-1.5 inline-flex items-center rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-800">
                            {patientCount}
                          </span>
                        </Label>
                      </div>
                    );
                  })}
              </div>

              {/* Helper text for states where doctor doesn't prescribe */}
              {availableStates.some(
                (state) => !doctorPrescribesInState(state.id),
              ) && (
                <div className="mt-2 rounded-md bg-orange-50 p-2">
                  <p className="text-xs text-orange-800">
                    <span className="font-medium">Note:</span> States marked
                    with <span className="font-bold text-orange-600">!</span>{' '}
                    indicate the doctor has patients but no longer prescribes
                    there.
                  </p>
                </div>
              )}
            </div>

            {/* Doctors filtered by selected states */}
            {selectedStates.length > 0 && (
              <div className="mb-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="text-sm font-medium">
                    Select Target Doctors{' '}
                    {(eligibleDoctors || []).length === 0 &&
                      '(No eligible doctors for selected states)'}
                    :
                  </h4>
                  {(eligibleDoctors || []).length > 0 && (
                    <button
                      className="cursor-pointer text-sm font-medium text-orange hover:underline"
                      onClick={() => {
                        if (
                          selectedDoctors.length ===
                          (eligibleDoctors || []).length
                        ) {
                          onSelectedDoctorsChange([]);
                        } else {
                          onSelectedDoctorsChange(
                            (eligibleDoctors || []).map((doctor) => doctor.id),
                          );
                        }
                      }}
                    >
                      {selectedDoctors.length === (eligibleDoctors || []).length
                        ? 'Clear All'
                        : 'Select All'}
                    </button>
                  )}
                </div>
                <div className="grid max-w-full grid-cols-1 gap-2 sm:grid-cols-2">
                  {[...(eligibleDoctors || [])]
                    .sort((a, b) =>
                      a.user.lastName.localeCompare(b.user.lastName),
                    )
                    .map((doctor) => (
                      <div
                        key={doctor.id}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`doctor-${doctor.id}`}
                          checked={selectedDoctors.includes(doctor.id)}
                          className="border-gray-300 data-[state=unchecked]:bg-white"
                          onCheckedChange={(checked) => {
                            if (checked) {
                              onSelectedDoctorsChange([
                                ...selectedDoctors,
                                doctor.id,
                              ]);
                            } else {
                              onSelectedDoctorsChange(
                                selectedDoctors.filter(
                                  (id) => id !== doctor.id,
                                ),
                              );
                            }
                          }}
                        />
                        <Label
                          htmlFor={`doctor-${doctor.id}`}
                          className="cursor-pointer text-sm font-normal"
                        >
                          Dr. {doctor.user.firstName} {doctor.user.lastName}
                        </Label>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </>
        ) : (
          <>
            {/* Doctors Selection First */}
            <div className="mb-4">
              <div className="mb-2 flex items-center justify-between">
                <h4 className="text-sm font-medium">Select Target Doctors:</h4>
                {availableDoctors.length > 0 && (
                  <button
                    className="cursor-pointer text-sm font-medium text-orange hover:underline"
                    onClick={() => {
                      // Filter out the source doctor
                      const filteredDoctors = availableDoctors.filter(
                        (doctor) => doctor.id !== sourceDoctor?.id,
                      );

                      if (selectedDoctors.length === filteredDoctors.length) {
                        onSelectedDoctorsChange([]);
                      } else {
                        onSelectedDoctorsChange(
                          filteredDoctors.map((doctor) => doctor.id),
                        );
                      }
                    }}
                  >
                    {selectedDoctors.length ===
                    (availableDoctors || []).filter(
                      (d) => d.id !== sourceDoctor?.id,
                    ).length
                      ? 'Clear All'
                      : 'Select All'}
                  </button>
                )}
              </div>
              <div className="grid max-w-full grid-cols-1 gap-2 sm:grid-cols-2">
                {[...(availableDoctors || [])]
                  .filter((doctor) => doctor.id !== sourceDoctor?.id)
                  .sort((a, b) =>
                    a.user.lastName.localeCompare(b.user.lastName),
                  )
                  .map((doctor) => (
                    <div
                      key={doctor.id}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`doctor-${doctor.id}`}
                        checked={selectedDoctors.includes(doctor.id)}
                        className="border-gray-300 data-[state=unchecked]:bg-white"
                        onCheckedChange={(checked) => {
                          if (checked) {
                            onSelectedDoctorsChange([
                              ...selectedDoctors,
                              doctor.id,
                            ]);
                          } else {
                            onSelectedDoctorsChange(
                              selectedDoctors.filter((id) => id !== doctor.id),
                            );
                          }
                        }}
                      />
                      <Label
                        htmlFor={`doctor-${doctor.id}`}
                        className="cursor-pointer text-sm font-normal"
                      >
                        Dr. {doctor.user.firstName} {doctor.user.lastName}
                      </Label>
                    </div>
                  ))}
              </div>
            </div>

            {/* States filtered by selected doctors */}
            {selectedDoctors.length > 0 && (
              <div className="mb-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="text-sm font-medium">
                    Select States{' '}
                    {eligibleStates.length === 0 &&
                      '(No common states for selected doctors)'}
                    :
                  </h4>
                  {eligibleStates.length > 0 && (
                    <button
                      className="cursor-pointer text-sm font-medium text-orange hover:underline"
                      onClick={() => {
                        if (selectedStates.length === eligibleStates.length) {
                          onSelectedStatesChange([]);
                        } else {
                          onSelectedStatesChange(
                            eligibleStates.map((state) => state.id),
                          );
                        }
                      }}
                    >
                      {selectedStates.length === eligibleStates.length
                        ? 'Clear All'
                        : 'Select All'}
                    </button>
                  )}
                </div>
                <div className="grid max-w-full grid-cols-2 gap-2 sm:grid-cols-3">
                  {[...eligibleStates]
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map((state) => {
                      // Find patient count for this state
                      const patientCount =
                        sourceDoctor?.statePatientCounts?.find(
                          (s) => s.stateId === state.id,
                        )?.patientCount || 0;

                      const prescribesInState = doctorPrescribesInState(
                        state.id,
                      );

                      return (
                        <div
                          key={state.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`state-${state.id}`}
                            checked={selectedStates.includes(state.id)}
                            className="border-gray-300 data-[state=unchecked]:bg-white"
                            onCheckedChange={(checked) => {
                              if (checked) {
                                onSelectedStatesChange([
                                  ...selectedStates,
                                  state.id,
                                ]);
                              } else {
                                onSelectedStatesChange(
                                  selectedStates.filter(
                                    (id) => id !== state.id,
                                  ),
                                );
                              }
                            }}
                          />
                          <Label
                            htmlFor={`state-${state.id}`}
                            className={`flex cursor-pointer items-center text-sm font-normal ${
                              !prescribesInState ? 'text-orange-600' : ''
                            }`}
                            title={
                              !prescribesInState
                                ? 'Doctor has patients but no longer prescribes in this state'
                                : ''
                            }
                          >
                            {state.name}
                            {!prescribesInState && (
                              <span className="ml-1 text-orange-600">!</span>
                            )}
                            <span className="ml-1.5 inline-flex items-center rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-800">
                              {patientCount}
                            </span>
                          </Label>
                        </div>
                      );
                    })}
                </div>

                {/* Helper text for states where doctor doesn't prescribe */}
                {eligibleStates.some(
                  (state) => !doctorPrescribesInState(state.id),
                ) && (
                  <div className="mt-2 rounded-md bg-orange-50 p-2">
                    <p className="text-xs text-orange-800">
                      <span className="font-medium">Note:</span> States marked
                      with <span className="font-bold text-orange-600">!</span>{' '}
                      indicate the doctor has patients but no longer prescribes
                      there.
                    </p>
                  </div>
                )}
              </div>
            )}
          </>
        )}

        {selectedStates.length > 0 && totalPatientsCount > 0 && (
          <div className="mb-4 rounded-md bg-gray-50 p-3">
            <p className="text-sm font-medium text-gray-600">
              Total: {totalPatientsCount} patients will be transferred
            </p>
          </div>
        )}

        <div className="flex justify-end space-x-2">
          <Button onClick={onCancel}>Cancel</Button>
          <Button
            onClick={onSave}
            disabled={
              selectedStates.length === 0 || selectedDoctors.length === 0
            }
          >
            Save Rule
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-4 w-full overflow-hidden rounded-md border border-gray-200 p-4">
      <div className="mb-2 flex items-center justify-between">
        <h3 className="text-md font-medium">Transfer Rule {index + 1}</h3>
        <div className="flex space-x-2">
          <Button
            size="sm"
            onClick={() => onSave()} // Trigger edit mode
          >
            Edit
          </Button>
          <Button size="sm" onClick={onDelete}>
            Delete
          </Button>
        </div>
      </div>

      <div className="mb-2">
        <h4 className="text-sm font-medium">Selected States:</h4>
        <div className="mt-1 flex max-w-full flex-wrap gap-1">
          {(availableStates || [])
            .filter((state) => selectedStates.includes(state.id))
            .sort((a, b) => a.name.localeCompare(b.name))
            .map((state) => (
              <span
                key={state.id}
                className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
              >
                {state.name}
              </span>
            ))}
        </div>
      </div>

      <div className="mb-2">
        <h4 className="text-sm font-medium">Target Doctors:</h4>
        <div className="mt-1 flex max-w-full flex-wrap gap-1">
          {(availableDoctors || [])
            .filter((doctor) => selectedDoctors.includes(doctor.id))
            .sort((a, b) => a.user.lastName.localeCompare(b.user.lastName))
            .map((doctor) => (
              <span
                key={doctor.id}
                className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
              >
                Dr. {doctor.user.firstName} {doctor.user.lastName}
                {selectedDoctors.length > 1 &&
                  (patientsPerDoctor[doctor.id] ?? 0) > 0 && (
                    <span className="ml-1 inline-flex items-center rounded-full bg-green-50 px-1.5 py-0.5 text-xs font-medium text-green-800">
                      {patientsPerDoctor[doctor.id]}
                    </span>
                  )}
              </span>
            ))}
        </div>
      </div>

      {totalPatientsCount > 0 && (
        <div className="mt-3 rounded-md bg-gray-50 p-2">
          <p className="text-sm text-gray-600">
            <span className="font-medium">{totalPatientsCount} patients</span>{' '}
            will be transferred with this rule
          </p>
        </div>
      )}
    </div>
  );
};

interface ExtendedPatientTransferDrawerProps
  extends PatientTransferDrawerProps {
  existingTransfer?: any; // The existing transfer object if editing
}

export function PatientTransferDrawer({
  isOpen,
  onClose,
  sourceDoctor,
  mode,
  existingTransfer,
}: ExtendedPatientTransferDrawerProps) {
  // Fetch all active doctors and all enabled states
  const { data: activeDoctors = [], isLoading: isLoadingDoctors } =
    useGetAllActiveDoctors();
  const { data: enabledStates = [], isLoading: isLoadingStates } =
    useGetAllEnabledStates();

  // Convert fetched data to expected format
  const availableDoctors = activeDoctors as Doctor[];
  const states = enabledStates as State[];
  // Separate rules for transfer and out-of-office modes
  const [transferRules, setTransferRules] = useState<
    {
      id: string;
      stateIds: string[];
      targetDoctorIds: string[];
      isEditing: boolean;
    }[]
  >([]);

  const [outOfOfficeRules, setOutOfOfficeRules] = useState<
    {
      id: string;
      stateIds: string[];
      targetDoctorIds: string[];
      isEditing: boolean;
    }[]
  >([]);

  // Active rules for current mode
  const rules = mode === 'outOfOffice' ? outOfOfficeRules : transferRules;

  // Function to set rules based on current mode
  const setRules = (newRules: typeof transferRules) => {
    if (mode === 'outOfOffice') {
      setOutOfOfficeRules(newRules);
    } else {
      setTransferRules(newRules);
    }
  };

  // Creation states - these can be transient
  const [isCreatingRule, setIsCreatingRule] = useState(false);
  const [newRuleStates, setNewRuleStates] = useState<string[]>([]);
  const [newRuleDoctors, setNewRuleDoctors] = useState<string[]>([]);

  // Separate state for transfer mode
  const [transferReason, setTransferReason] = useState('Doctor reassignment');
  const [transferModeTransferAt, setTransferModeTransferAt] =
    useState<Date | null>(null);

  // Separate state for out-of-office mode
  const [outOfOfficeReason] = useState('Doctor out of office'); // Fixed reason
  const [outOfOfficeTransferAt, setOutOfOfficeTransferAt] =
    useState<Date | null>(null);
  const [outOfOfficeRevertAt, setOutOfOfficeRevertAt] = useState<Date | null>(
    null,
  );

  // State for calendar popovers
  const [transferAtPopoverOpen, setTransferAtPopoverOpen] = useState(false);
  const [revertAtPopoverOpen, setRevertAtPopoverOpen] = useState(false);

  // Derived values based on current mode
  const isOutOfOffice = mode === 'outOfOffice';
  const transferAt =
    mode === 'outOfOffice' ? outOfOfficeTransferAt : transferModeTransferAt;
  const revertAt = outOfOfficeRevertAt;

  const { mutateAsync: transferPatients, isPending: isTransferPending } =
    useTransferPatients();
  const { mutateAsync: deleteBulkTransfer, isPending: isDeletePending } =
    useDeleteBulkTransfer();
  const { mutateAsync: updateBulkTransfer, isPending: isUpdatePending } =
    useUpdateBulkTransfer();

  // Combined loading state
  const isPending =
    isTransferPending ||
    isDeletePending ||
    isUpdatePending ||
    isLoadingDoctors ||
    isLoadingStates;

  // Reset form states when drawer opens
  useEffect(() => {
    if (isOpen) {
      // Always reset these transient states when drawer first opens
      setIsCreatingRule(false);
      setNewRuleStates([]);
      setNewRuleDoctors([]);

      // If we have an existing transfer to edit, populate the form with its data
      if (existingTransfer) {
        console.log('Loading existing transfer:', existingTransfer);

        // Set common fields
        if (existingTransfer.reason) {
          if (mode === 'outOfOffice') {
            // Keep the default out of office reason
          } else {
            setTransferReason(existingTransfer.reason);
          }
        }

        // Set dates
        if (existingTransfer.transferAt) {
          const transferAtDate = new Date(existingTransfer.transferAt);
          if (mode === 'outOfOffice') {
            setOutOfOfficeTransferAt(transferAtDate);
          } else {
            setTransferModeTransferAt(transferAtDate);
          }
        }

        // Set revert date for out-of-office mode
        if (mode === 'outOfOffice' && existingTransfer.revertAt) {
          setOutOfOfficeRevertAt(new Date(existingTransfer.revertAt));
        }

        // Set transfer rules based on the existing rules
        if (existingTransfer.rules && Array.isArray(existingTransfer.rules)) {
          const loadedRules = existingTransfer.rules.map((rule: any) => ({
            id: Math.random().toString(36).substring(7), // Generate new IDs for the UI
            stateIds: rule.stateIds || [],
            targetDoctorIds: rule.targetDoctorIds || [],
            isEditing: false,
          }));

          if (mode === 'outOfOffice') {
            setOutOfOfficeRules(loadedRules);
          } else {
            setTransferRules(loadedRules);
          }
        }
      } else {
        // Only initialize transfer dates and reasons if empty for their respective modes
        if (mode === 'outOfOffice') {
          // For outOfOffice mode, if no rules are set, set a default revert date
          if (outOfOfficeRules.length === 0) {
            const oneWeekFromNow = new Date();
            oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);
            setOutOfOfficeRevertAt(oneWeekFromNow);
          }
        } else {
          // For transfer mode, only reset reason if no rules
          if (transferRules.length === 0) {
            setTransferReason('Doctor reassignment');
          }
        }
      }
    }
  }, [
    isOpen,
    mode,
    existingTransfer,
    outOfOfficeRules.length,
    transferRules.length,
  ]);

  // Calculate which states are already covered by existing rules
  // When we're editing a rule, we need to exclude its states from being considered "covered"
  // so that they're available for selection in the current edit
  const coveredStateIds = useMemo(() => {
    return rules
      .filter((rule) => !rule.isEditing)
      .flatMap((rule) => rule.stateIds);
  }, [rules]);

  // Filter available states to only show those not yet covered
  const availableStates = useMemo(() => {
    if (!sourceDoctor || isLoadingStates) return [];

    // Extract all states where the doctor has patients
    // Using statePatientCounts which contains the actual states with patients
    const doctorStatesWithPatients =
      sourceDoctor.statePatientCounts
        ?.filter(
          (stateCount) => stateCount.patientCount > 0 && stateCount.enabled,
        )
        .map((stateCount) => stateCount.stateId) || [];

    return states.filter(
      (state) =>
        doctorStatesWithPatients.includes(state.id) &&
        !coveredStateIds.includes(state.id) &&
        state.enabled,
    );
  }, [states, sourceDoctor, coveredStateIds, isLoadingStates]);

  // Get all states with patients for validation
  const allStatesWithPatients = useMemo(() => {
    if (!sourceDoctor || isLoadingStates) return [];

    return (sourceDoctor.statePatientCounts || [])
      .filter((stateCount) => stateCount.patientCount > 0 && stateCount.enabled)
      .map((stateCount) => stateCount.stateId);
  }, [sourceDoctor, isLoadingStates]);

  // Check if all states with patients are covered
  const allStatesCovered = useMemo(() => {
    if (!sourceDoctor || allStatesWithPatients.length === 0) return false;

    return allStatesWithPatients.every((stateId) =>
      coveredStateIds.includes(stateId),
    );
  }, [sourceDoctor, coveredStateIds, allStatesWithPatients]);

  const handleAddRule = () => {
    setIsCreatingRule(true);
    setNewRuleStates([]);
    setNewRuleDoctors([]);
  };

  const handleSaveNewRule = () => {
    if (newRuleStates.length > 0 && newRuleDoctors.length > 0) {
      setRules([
        ...rules,
        {
          id: Math.random().toString(36).substring(7),
          stateIds: newRuleStates,
          targetDoctorIds: newRuleDoctors,
          isEditing: false,
        },
      ]);
      setIsCreatingRule(false);
      setNewRuleStates([]);
      setNewRuleDoctors([]);
    }
  };

  const handleCancelNewRule = () => {
    setIsCreatingRule(false);
    setNewRuleStates([]);
    setNewRuleDoctors([]);
  };

  const handleDeleteRule = (id: string) => {
    setRules(rules.filter((rule) => rule.id !== id));
  };

  // Modified function to update an existing transfer or create a new one
  const handleTransferPatients = async () => {
    if (!sourceDoctor || rules.length === 0) {
      console.log('No source doctor or empty rules - returning');
      return;
    }

    // No longer verify that all states are covered - partial transfers are allowed

    try {
      // Always add revertAt for outOfOffice mode, even if it's null (for debugging)
      const transferData: any = {
        sourceDoctorId: sourceDoctor.id,
        reason: mode === 'outOfOffice' ? outOfOfficeReason : transferReason,
        transferBlocks: rules.map((rule) => ({
          stateIds: rule.stateIds,
          targetDoctorIds: rule.targetDoctorIds,
        })),
        ...(transferAt && { transferAt }),
        ...(mode === 'outOfOffice' && { revertAt }),
      };

      console.log('Transfer data:', transferData);

      // If we're editing an existing transfer, use the update endpoint
      if (existingTransfer && sourceDoctor) {
        // Prepare update data matching the expected UpdateBulkTransferData interface
        const updateData = {
          reason: transferData.reason,
          transferBlocks: transferData.transferBlocks.map((block: any) => ({
            stateIds: block.stateIds,
            targetDoctorIds: block.targetDoctorIds,
          })),
          transferAt: transferData.transferAt,
          revertAt: transferData.revertAt,
        };

        // Use the update hook
        await updateBulkTransfer({
          bulkTransferId: existingTransfer.id,
          doctorId: sourceDoctor.id,
          updateData: updateData,
        });
      } else {
        // Create a new transfer
        await transferPatients(transferData);
      }

      // Determine the correct message based on context
      let title, description;

      if (existingTransfer) {
        title = 'Transfer Updated';
        description = 'The transfer has been updated successfully.';
      } else {
        if (transferAt) {
          title = 'Patient Transfer Scheduled';
          description = `The patient transfer has been scheduled for ${transferAt.toLocaleDateString()} ${transferAt.toLocaleTimeString()}.`;
        } else {
          title = 'Patients Transfer Initiated';
          description =
            'The patient transfer has been queued for immediate processing.';
        }
      }

      toast({ title, description });

      // Clear the local data after creating/updating a transfer
      if (mode === 'outOfOffice') {
        setOutOfOfficeRules([]);
        setOutOfOfficeTransferAt(null);
        setOutOfOfficeRevertAt(null);
      } else {
        setTransferRules([]);
        setTransferModeTransferAt(null);
        setTransferReason('Doctor reassignment');
      }

      onClose();
    } catch (error) {
      console.error('Error transferring patients:', error);
      toast({
        title: 'Error',
        description: 'Failed to transfer patients. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Function to delete an existing transfer
  const handleDeleteTransfer = async () => {
    if (!existingTransfer || !sourceDoctor) {
      console.error('Cannot delete - no existing transfer or source doctor');
      return;
    }

    if (!window.confirm('Are you sure you want to delete this transfer?')) {
      return;
    }

    try {
      // Use the hook to delete the transfer with the doctor ID
      await deleteBulkTransfer({
        bulkTransferId: existingTransfer.id,
        doctorId: sourceDoctor.id,
      });

      toast({
        title: 'Transfer Deleted',
        description: 'The scheduled transfer has been deleted.',
      });

      // Clear the local data after deleting a transfer
      if (mode === 'outOfOffice') {
        setOutOfOfficeRules([]);
        setOutOfOfficeTransferAt(null);
        setOutOfOfficeRevertAt(null);
      } else {
        setTransferRules([]);
        setTransferModeTransferAt(null);
        setTransferReason('Doctor reassignment');
      }

      onClose();
    } catch (error) {
      console.error('Error deleting transfer:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete the transfer. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Drawer
      open={isOpen}
      onOpenChange={onClose}
      direction="right"
      modal={true}
      dismissible={true}
    >
      <DrawerOverlay className="fixed inset-0 z-50 bg-black/25" />
      <DrawerContent className="fixed right-0 top-0 z-50 m-0 flex h-full w-[550px] max-w-full flex-col overflow-hidden border-l border-border bg-white p-0 shadow-lg">
        {/* Header */}
        <div className="flex h-[62px] shrink-0 items-center justify-between border-b px-6">
          <div>
            <h2 className="text-lg font-medium">
              {existingTransfer ? 'Scheduled ' : ''}
              {mode === 'outOfOffice'
                ? 'Set Out of Office'
                : 'Transfer Patients'}
            </h2>
            {sourceDoctor && (
              <p className="text-sm text-gray-500">
                From Dr. {sourceDoctor.user.firstName}{' '}
                {sourceDoctor.user.lastName}
              </p>
            )}
          </div>
          <XIcon size={24} className="cursor-pointer" onClick={onClose} />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden px-6 py-6">
          {/* Loading indicator */}
          {(isLoadingDoctors || isLoadingStates) && (
            <div className="flex items-center justify-center pb-4">
              <Loader className="h-8 w-8 text-denim" />
              <span className="ml-2 text-denim">Loading...</span>
            </div>
          )}

          <div className="w-full space-y-6">
            {!isLoadingDoctors &&
              !isLoadingStates &&
              sourceDoctor?.totalPatientCount !== undefined && (
                <div className="mb-6 rounded-md bg-gray-50 p-4">
                  <h3 className="text-md font-medium">
                    Doctor's Patient Overview
                  </h3>
                  <p className="mt-2 text-sm text-gray-700">
                    <span className="font-medium">Total Patients:</span>{' '}
                    <span className="rounded-full bg-denim-light/20 px-2.5 py-1 text-sm font-medium text-denim">
                      {sourceDoctor.totalPatientCount}
                    </span>
                  </p>
                  {sourceDoctor.statePatientCounts &&
                    sourceDoctor.statePatientCounts.length > 0 && (
                      <div className="mt-3">
                        <p className="mb-1 text-sm font-medium text-gray-700">
                          Distribution by state:
                        </p>
                        <div className="flex max-w-full flex-wrap gap-1.5">
                          {sourceDoctor.statePatientCounts
                            .filter((state) => state.patientCount > 0)
                            .sort((a, b) =>
                              a.stateName.localeCompare(b.stateName),
                            )
                            .map((state) => (
                              <span
                                key={state.stateId}
                                className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs"
                              >
                                {state.stateName}:{' '}
                                <span className="ml-1 font-medium">
                                  {state.patientCount}
                                </span>
                              </span>
                            ))}
                        </div>
                      </div>
                    )}
                </div>
              )}

            {/* Only show reason field in transfer mode */}
            {mode === 'transfer' && (
              <div className="mb-6">
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Reason for Transfer
                </label>
                <input
                  type="text"
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                  value={transferReason}
                  onChange={(e) => setTransferReason(e.target.value)}
                  maxLength={255}
                />
              </div>
            )}

            {/* Transfer scheduling and Out of Office options */}
            <div className="mb-6 space-y-4 rounded-md border border-gray-200 p-4">
              <h3 className="text-md font-medium">
                {mode === 'outOfOffice'
                  ? 'Out of Office Scheduling'
                  : 'Transfer Scheduling'}
              </h3>

              {mode === 'outOfOffice' && (
                <p className="mb-3 mt-1 text-sm text-gray-600">
                  When setting a doctor out-of-office, you must specify when the
                  patients should be reverted back to this doctor.
                </p>
              )}

              {/* Transfer At Date Picker */}
              <div className="mb-4">
                <div className="mb-2 flex items-center">
                  {/* Using controlled popover with state */}
                  <Popover
                    open={transferAtPopoverOpen}
                    onOpenChange={setTransferAtPopoverOpen}
                  >
                    <PopoverTrigger asChild>
                      <a className="h-auto cursor-pointer p-0 text-sm font-normal underline">
                        {mode === 'outOfOffice'
                          ? 'Out of Office Start Date (Optional)'
                          : 'Transfer Date (Optional)'}
                      </a>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={transferAt || undefined}
                        onSelect={(date) => {
                          if (mode === 'outOfOffice') {
                            setOutOfOfficeTransferAt(date ?? new Date());
                          } else {
                            setTransferModeTransferAt(date ?? new Date());
                          }
                          // Close the popover after selection
                          setTransferAtPopoverOpen(false);
                        }}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="flex items-center text-sm text-blue-600">
                  {transferAt ? (
                    <>
                      <span>
                        Transfer will be executed on {format(transferAt, 'PPP')}
                      </span>
                      <button
                        onClick={() => {
                          if (mode === 'outOfOffice') {
                            setOutOfOfficeTransferAt(null);
                          } else {
                            setTransferModeTransferAt(null);
                          }
                        }}
                        className="ml-2 rounded-sm bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 hover:bg-blue-200"
                      >
                        Clear
                      </button>
                    </>
                  ) : (
                    <span>Transfer will be executed immediately</span>
                  )}
                </div>
              </div>

              {/* Revert At Date Picker (only shown in outOfOffice mode) */}
              {mode === 'outOfOffice' && (
                <div className="mb-4 mt-4">
                  <div className="mb-2 flex items-center">
                    <Popover
                      open={revertAtPopoverOpen}
                      onOpenChange={setRevertAtPopoverOpen}
                    >
                      <PopoverTrigger asChild>
                        <a className="h-auto cursor-pointer p-0 text-sm font-normal underline">
                          Revert Date (Required)
                        </a>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={revertAt || undefined}
                          onSelect={(date) => {
                            console.log('Selected revert date:', date);
                            setOutOfOfficeRevertAt(date ?? new Date());
                            // Close the popover after selection
                            setRevertAtPopoverOpen(false);
                          }}
                          disabled={(date) => {
                            // Ensure we always return a boolean, not null
                            if (!date) return true;

                            const today = new Date();
                            // Revert date must be after today and after transferAt (if set)
                            return (
                              date < today ||
                              (transferAt ? date <= transferAt : false)
                            );
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  {revertAt && (
                    <div className="flex items-center text-sm text-blue-600">
                      <span>
                        Transfer will be automatically reverted on{' '}
                        {format(revertAt, 'PPP')}
                      </span>
                      {/* No clear button in out-of-office mode since revert date is required */}
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="mb-6">
              <div className="mb-2 flex items-center justify-between">
                <h3 className="text-md font-medium">Transfer Rules</h3>
                <Button
                  onClick={handleAddRule}
                  disabled={
                    isCreatingRule ||
                    availableStates.length === 0 ||
                    isLoadingDoctors ||
                    isLoadingStates
                  }
                >
                  Add Rule
                </Button>
              </div>

              {rules.map((rule, index) => {
                // Get states that are covered by other rules (not this one)
                const otherRulesCoveredStateIds = rules
                  .filter((r) => r.id !== rule.id && !r.isEditing)
                  .flatMap((r) => r.stateIds);

                // Get available states for this rule:
                // 1. Include all states this rule already has selected
                // 2. Include states not covered by other rules
                const ruleAvailableStates = states.filter(
                  (state) =>
                    // Always include states that are already selected in this rule
                    rule.stateIds.includes(state.id) ||
                    // Or include states not covered by other rules
                    (!otherRulesCoveredStateIds.includes(state.id) &&
                      // Only show states with patients if the doctor has any
                      (!sourceDoctor?.statePatientCounts ||
                        sourceDoctor.statePatientCounts.some(
                          (sc) =>
                            sc.stateId === state.id &&
                            sc.patientCount > 0 &&
                            sc.enabled,
                        ))),
                );

                return (
                  <TransferRule
                    key={rule.id}
                    index={index}
                    availableDoctors={availableDoctors}
                    availableStates={ruleAvailableStates}
                    selectedStates={rule.stateIds}
                    onSelectedStatesChange={(stateIds) => {
                      setRules(
                        rules.map((r) =>
                          r.id === rule.id ? { ...r, stateIds } : r,
                        ),
                      );
                    }}
                    selectedDoctors={rule.targetDoctorIds}
                    onSelectedDoctorsChange={(doctorIds) => {
                      setRules(
                        rules.map((r) =>
                          r.id === rule.id
                            ? { ...r, targetDoctorIds: doctorIds }
                            : r,
                        ),
                      );
                    }}
                    isEditing={rule.isEditing}
                    onSave={() => {
                      // Toggle editing mode
                      setRules(
                        rules.map((r) =>
                          r.id === rule.id
                            ? { ...r, isEditing: !r.isEditing }
                            : r,
                        ),
                      );
                    }}
                    onCancel={() => {
                      setRules(
                        rules.map((r) =>
                          r.id === rule.id ? { ...r, isEditing: false } : r,
                        ),
                      );
                    }}
                    onDelete={() => handleDeleteRule(rule.id)}
                    sourceDoctor={sourceDoctor}
                  />
                );
              })}

              {isCreatingRule && (
                <TransferRule
                  index={rules.length}
                  availableDoctors={availableDoctors}
                  availableStates={availableStates}
                  selectedStates={newRuleStates}
                  onSelectedStatesChange={setNewRuleStates}
                  selectedDoctors={newRuleDoctors}
                  onSelectedDoctorsChange={setNewRuleDoctors}
                  isEditing={true}
                  onSave={handleSaveNewRule}
                  onCancel={handleCancelNewRule}
                  onDelete={() => {
                    /* no-op */
                  }}
                  sourceDoctor={sourceDoctor}
                />
              )}

              {!isCreatingRule &&
                availableStates.length > 0 &&
                rules.length === 0 && (
                  <div className="rounded-md border border-dashed border-gray-300 p-4 text-center text-sm text-gray-500">
                    No transfer rules created yet. Click "Add Rule" to begin.
                  </div>
                )}

              {!isCreatingRule && allStatesWithPatients.length === 0 && (
                <div className="rounded-md border border-dashed border-gray-300 p-4 text-center text-sm text-gray-500">
                  This doctor doesn't have any patients to transfer.
                </div>
              )}

              {!isCreatingRule &&
                allStatesWithPatients.length > 0 &&
                availableStates.length === 0 &&
                rules.length > 0 && (
                  <div className="rounded-md border border-dashed border-gray-300 p-4 text-center text-sm text-gray-500">
                    All states with patients have been assigned to transfer
                    rules.
                  </div>
                )}
            </div>

            {!allStatesCovered && rules.length > 0 && (
              <div className="rounded-md bg-blue-50 p-3">
                <div className="text-sm text-blue-800">
                  <p>
                    You are setting up a partial transfer. Some states with
                    patients are not included in your transfer rules.
                  </p>

                  {/* Only show states that have patients and aren't covered */}
                  {sourceDoctor?.statePatientCounts && (
                    <div className="mt-2">
                      <p className="font-medium">
                        States with patients not included in transfer:
                      </p>
                      <div className="mt-1 flex max-w-full flex-wrap gap-1">
                        {states
                          .filter(
                            (state) =>
                              allStatesWithPatients.includes(state.id) &&
                              !coveredStateIds.includes(state.id) &&
                              // Ensure the state has patients
                              sourceDoctor.statePatientCounts?.some(
                                (stateCount) =>
                                  stateCount.stateId === state.id &&
                                  stateCount.patientCount > 0,
                              ),
                          )
                          .sort((a, b) => a.name.localeCompare(b.name))
                          .map((state) => {
                            // Get the patient count for this state
                            const patientCount =
                              sourceDoctor.statePatientCounts?.find(
                                (sc) => sc.stateId === state.id,
                              )?.patientCount || 0;

                            return (
                              <span
                                key={state.id}
                                className="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800"
                              >
                                {state.name}
                                <span className="ml-1 font-semibold">
                                  ({patientCount})
                                </span>
                              </span>
                            );
                          })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex h-[75px] shrink-0 items-center justify-between overflow-hidden border-t px-6">
          {/* Delete button (only shown when editing an existing transfer) */}
          <div>
            {existingTransfer && (
              <Button
                variant="destructive"
                onClick={handleDeleteTransfer}
                disabled={isPending}
              >
                Delete Transfer
              </Button>
            )}
          </div>

          <div className="flex space-x-3">
            <Button onClick={onClose}>Cancel</Button>
            <Button
              onClick={handleTransferPatients}
              disabled={
                rules.length === 0 ||
                isPending ||
                (mode === 'outOfOffice' && !revertAt)
              }
            >
              {isPending ? <Loader className="mr-2 h-4 w-4" /> : null}
              {existingTransfer
                ? 'Update Transfer'
                : mode === 'outOfOffice'
                  ? transferAt
                    ? 'Schedule Out of Office'
                    : 'Set Out of Office'
                  : transferAt
                    ? 'Schedule Transfer'
                    : 'Queue Immediate Transfer'}
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
