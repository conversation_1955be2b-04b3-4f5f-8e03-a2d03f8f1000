'use client';

import { useEffect, useRef } from 'react';
import { useAtom } from 'jotai';

import type { ChatParticipant, Message } from '@willow/chat';
import {
  ChatProvider,
  groupMessagesByDay,
  MessageCardInfo,
  MessageInput,
  useChat,
} from '@willow/chat';
import { Banner } from '@willow/ui/base/banner';
import { Loader } from '@willow/ui/loader';
import { accessTokenAtom } from '@willow/utils/api/auth';
import { formatDate } from '@willow/utils/format';

import { env } from '~/env';
import { useCurrentDoctor } from '~/hooks/doctor';
import { useDoctorAssetLink } from '~/hooks/links';

export const DoctorAdminConversation = ({
  conversationId,
}: {
  conversationId: string;
}) => {
  const [signIn] = useAtom(accessTokenAtom);

  if (!signIn?.accessToken) {
    return <Loader className="h-screen" size="xl" />;
  }

  return (
    <ChatProvider
      apiUrl={env.NEXT_PUBLIC_API_URL}
      s3Url={env.NEXT_PUBLIC_API_S3_URL}
      accessToken={signIn.accessToken}
      conversationId={conversationId}
      autoMarkAsRead
      type="doctorAdmin"
    >
      <Chat />
    </ChatProvider>
  );
};

export const Chat = () => {
  const { messages, isLoading: chatLoading, conversation } = useChat();
  const isLoading = chatLoading;

  if (!conversation) return null;

  const isClosedConversation = conversation.status === 'closed';

  return (
    <div className="relative flex h-full min-w-0 flex-col">
      {isClosedConversation && conversation.closedAt && (
        <ClosedConversationBanner closedAt={conversation.closedAt} />
      )}
      <div
        className={`flex min-w-0 flex-1 flex-col gap-6 overflow-y-scroll px-8 ${isClosedConversation ? 'pt-10' : 'pt-4'}`}
      >
        {isLoading && <Loader className="h-full" size="md" />}
        {!isLoading && messages.length === 0 && <EmptyChat />}

        {!isLoading && messages.length > 0 && (
          <RenderMessages
            messages={messages}
            participants={conversation.participants}
          />
        )}
      </div>
      {!isClosedConversation && (
        <div className="w-full gap-2 px-8 pb-4 md:pb-6">
          <MessageInput conversation={conversation} />
        </div>
      )}
    </div>
  );
};

export const RenderMessages = ({
  messages,
  participants,
}: {
  messages: Message[];
  participants: Record<string, ChatParticipant>;
}) => {
  const doctor = useCurrentDoctor();
  const { genAssetLink } = useDoctorAssetLink();

  const bottomRef = useRef<HTMLDivElement>(null);

  const groupedMessages = groupMessagesByDay(messages);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className="flex flex-col gap-8">
      {Object.keys(groupedMessages).map((date) => (
        <div className="flex flex-col gap-4" key={date}>
          <div className="text-center text-xs font-normal text-zinc-500">
            {date}
          </div>
          {groupedMessages[date]?.map((message) => (
            <MessageCardInfo
              key={message.id}
              message={message}
              profileId={doctor.userId}
              participants={participants}
              genAssetLink={genAssetLink}
            />
          ))}
        </div>
      ))}
      <div ref={bottomRef} style={{ float: 'left', clear: 'both' }} />
    </div>
  );
};

const EmptyChat = () => {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-2.5 pb-20">
      <div className="flex flex-col items-center justify-center">
        <div className="text-xl font-semibold text-denim">
          Start a conversation
        </div>
        <div className="text-center text-xl font-medium text-denim text-opacity-60">
          Send a message to begin chatting with this user
        </div>
      </div>
    </div>
  );
};

function ClosedConversationBanner({ closedAt }: { closedAt: Date }) {
  return (
    <Banner variant="warning" className="absolute top-0 p-1">
      This conversation was closed on {formatDate(new Date(closedAt))}
    </Banner>
  );
}
